{"__meta": {"id": "Xfd0528fa5a0d252d221d69f7430c97f8", "datetime": "2025-09-17 09:57:43", "utime": 1758092263.57082, "method": "POST", "uri": "/livewire/message/ues", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758092262.948517, "end": 1758092263.570856, "duration": 0.6223390102386475, "duration_str": "622ms", "measures": [{"label": "Booting", "start": 1758092262.948517, "relative_start": 0, "end": 1758092263.305003, "relative_end": 1758092263.305003, "duration": 0.35648584365844727, "duration_str": "356ms", "params": [], "collector": null}, {"label": "Application", "start": 1758092263.305798, "relative_start": 0.357280969619751, "end": 1758092263.570858, "relative_end": 1.9073486328125e-06, "duration": 0.2650599479675293, "duration_str": "265ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 27484328, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "livewire.deraq.ue.index (\\resources\\views\\livewire\\deraq\\ue\\index.blade.php)", "param_count": 40, "params": ["ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "errors", "_instance", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/index.blade.php&line=0"}, {"name": "livewire.deraq.ue.liste (\\resources\\views\\livewire\\deraq\\ue\\liste.blade.php)", "param_count": 42, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}, {"name": "livewire.deraq.ue.duplication-modal (\\resources\\views\\livewire\\deraq\\ue\\duplication-modal.blade.php)", "param_count": 48, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators", "__currentLoopData", "parcour", "loop", "niveau", "annee", "ue"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/duplication-modal.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.016819999999999998, "accumulated_duration_str": "16.82ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00543, "duration_str": "5.43ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 32.283}, {"sql": "select count(*) as aggregate from `ues` where `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 32.283, "width_percent": 4.4}, {"sql": "select * from `ues` where `ues`.`deleted_at` is null limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 36.683, "width_percent": 4.221}, {"sql": "select * from `parcours` where `parcours`.`id` in (2, 4, 5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 40.904, "width_percent": 3.864}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 44.768, "width_percent": 3.864}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00061, "duration_str": "610μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 48.633, "width_percent": 3.627}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 52.259, "width_percent": 4.816}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (80, 84, 85, 87, 88, 89, 90, 91, 92, 93) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 57.075, "width_percent": 5.886}, {"sql": "select * from `users` where `users`.`id` in (145, 147, 148, 149, 151, 155, 156, 157, 158, 159, 177, 180) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00131, "duration_str": "1.31ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 62.961, "width_percent": 7.788}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 108}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:108", "connection": "imsaaapp", "start_percent": 70.749, "width_percent": 4.34}, {"sql": "select * from `semestres` where `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 109}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:109", "connection": "imsaaapp", "start_percent": 75.089, "width_percent": 4.756}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 110}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:110", "connection": "imsaaapp", "start_percent": 79.845, "width_percent": 4.4}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 111}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:111", "connection": "imsaaapp", "start_percent": 84.245, "width_percent": 4.162}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 2) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 112}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00195, "duration_str": "1.95ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:112", "connection": "imsaaapp", "start_percent": 88.407, "width_percent": 11.593}]}, "models": {"data": {"App\\Models\\Matiere": 21, "App\\Models\\AnneeUniversitaire": 8, "App\\Models\\Semestre": 11, "App\\Models\\Niveau": 6, "App\\Models\\Parcour": 27, "App\\Models\\Ue": 10, "App\\Models\\User": 64}, "count": 147}, "livewire": {"data": {"ues #On9UEMExUvwvwtFDmk4t": "array:5 [\n  \"data\" => array:30 [\n    \"currentPage\" => \"liste\"\n    \"query\" => \"\"\n    \"selectedParcours\" => []\n    \"selectedNiveaux\" => []\n    \"filtreAnnee\" => \"\"\n    \"newUe\" => []\n    \"editUe\" => []\n    \"editingUeId\" => null\n    \"expandedUes\" => []\n    \"showQuickAddModal\" => false\n    \"showEcModal\" => false\n    \"ecModalMode\" => \"add\"\n    \"ecForm\" => []\n    \"currentEcUe\" => null\n    \"currentEcId\" => null\n    \"showDuplicationModal\" => true\n    \"duplicationStep\" => \"selection\"\n    \"selectedUesForDuplication\" => array:3 [\n      0 => 80\n      1 => 84\n      2 => 85\n    ]\n    \"targetParcours\" => array:1 [\n      0 => \"1\"\n    ]\n    \"targetAnneeId\" => \"3\"\n    \"duplicationPreview\" => array:1 [\n      80 => array:4 [\n        \"ue\" => array:8 [\n          \"id\" => 80\n          \"nom\" => \"ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALITÉ\"\n          \"code\" => \"IMTBL11EJF\"\n          \"credit\" => 7\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 3\n            \"nom\" => \"Comptabilité Générale\"\n            \"code\" => \"COMPTAGEN\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n          1 => array:6 [\n            \"id\" => 4\n            \"nom\" => \"Fiscalité\"\n            \"code\" => \"FISC\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n        ]\n        \"target_parcours\" => array:1 [\n          0 => array:3 [\n            \"id\" => 1\n            \"nom\" => \"Tourisme Hôtellerie et Restauration\"\n            \"sigle\" => \"THR\"\n          ]\n        ]\n        \"conflicts\" => []\n      ]\n    ]\n    \"editablePreviewData\" => array:1 [\n      80 => array:2 [\n        \"ue\" => array:8 [\n          \"id\" => 80\n          \"nom\" => \"ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALITÉ\"\n          \"code\" => \"IMTBL11EJF\"\n          \"credit\" => 7\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 3\n            \"nom\" => \"Comptabilité Générale\"\n            \"code\" => \"COMPTAGEN\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n          1 => array:6 [\n            \"id\" => 4\n            \"nom\" => \"Fiscalité\"\n            \"code\" => \"FISC\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n        ]\n      ]\n    ]\n    \"duplicationResults\" => []\n    \"enseignantQuery\" => \"\"\n    \"filteredEnseignants\" => []\n    \"showAddEnseignantForm\" => false\n    \"newEnseignant\" => array:5 [\n      \"nom\" => \"\"\n      \"prenom\" => \"\"\n      \"email\" => \"\"\n      \"telephone1\" => \"\"\n      \"sexe\" => \"M\"\n    ]\n    \"recentlyAssignedEnseignants\" => []\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"ues\"\n  \"view\" => \"livewire.deraq.ue.index\"\n  \"component\" => \"App\\Http\\Livewire\\Ues\"\n  \"id\" => \"On9UEMExUvwvwtFDmk4t\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pedagogiques/ue\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1758092143\n]"}, "request": {"path_info": "/livewire/message/ues", "status_code": "<pre class=sf-dump id=sf-dump-1709004996 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1709004996\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-884274914 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-884274914\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-865403444 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">On9UEMExUvwvwtFDmk4t</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ues</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">pedagogiques/ue</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">2e31d8d3</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:30</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>selectedParcours</span>\" => []\n      \"<span class=sf-dump-key>selectedNiveaux</span>\" => []\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => \"\"\n      \"<span class=sf-dump-key>newUe</span>\" => []\n      \"<span class=sf-dump-key>editUe</span>\" => []\n      \"<span class=sf-dump-key>editingUeId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>expandedUes</span>\" => []\n      \"<span class=sf-dump-key>showQuickAddModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEcModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>ecModalMode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">add</span>\"\n      \"<span class=sf-dump-key>ecForm</span>\" => []\n      \"<span class=sf-dump-key>currentEcUe</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentEcId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>showDuplicationModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>duplicationStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">selection</span>\"\n      \"<span class=sf-dump-key>selectedUesForDuplication</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-num>80</span>\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-num>84</span>\n      </samp>]\n      \"<span class=sf-dump-key>targetParcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>targetAnneeId</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>duplicationPreview</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>80</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>80</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"47 characters\">ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALIT&#201;</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">IMTBL11EJF</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Comptabilit&#233; G&#233;n&#233;rale</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"9 characters\">COMPTAGEN</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Fiscalit&#233;</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FISC</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>target_parcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Tourisme H&#244;tellerie et Restauration</span>\"\n              \"<span class=sf-dump-key>sigle</span>\" => \"<span class=sf-dump-str title=\"3 characters\">THR</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>conflicts</span>\" => []\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>editablePreviewData</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>80</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>80</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"47 characters\">ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALIT&#201;</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">IMTBL11EJF</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Comptabilit&#233; G&#233;n&#233;rale</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"9 characters\">COMPTAGEN</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Fiscalit&#233;</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FISC</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>duplicationResults</span>\" => []\n      \"<span class=sf-dump-key>enseignantQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>filteredEnseignants</span>\" => []\n      \"<span class=sf-dump-key>showAddEnseignantForm</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>newEnseignant</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>nom</span>\" => \"\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"\"\n        \"<span class=sf-dump-key>email</span>\" => \"\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"\"\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>M</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>recentlyAssignedEnseignants</span>\" => []\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">809ce66c1a3e1412509bba63d5d0acb1ede06742de37269aae4917f1c9b17b98</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5ls3</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"17 characters\">toggleUeSelection</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>85</span>\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-865403444\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-550558662 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2031</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IitVcHNzQTQ2YUwrNGs4c0ZXcFZKS1E9PSIsInZhbHVlIjoiRUppYUhualpMMTJpTVhDWTF5akxWNjdKRW4raWNzRld2c2ZKNExhRFBOM2N5UitPNXBvdVpNSHJXSU0wWVdVdFl5REJRVjJEVmFhS21GMXFTMjYxMU9ZcGp2djlwQmlZL3JOdDh0WlBXT1BVUWFhNFB2VmN4RmN4VmQwbVlBRGsiLCJtYWMiOiJjZTc2YTQ0ZmQ2NWJlMDIyNWZiY2FmMjM0ZGE4NjdlMDhhODlmYTZkZmIyY2U3N2FmMTZmZjJmMWY5N2Y4MzNkIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6InJoNUFZRHFmR1ZySTAvK0xRQ3QrSnc9PSIsInZhbHVlIjoidGV3bEZqWGZMYVlFUXZpZ3JkOTFVRXdLeVpXdTJZY0hZRFMrdk9iWXlqSkRMRGxTQWh0WTBEZVdmWUI0U2kvd3Z0WWhxR0UveVRlSGVQVk9uQzQweXI0ZEwxcGdCSWRWMXJ3cWtHUDlsUCtNdkhNNEFKK2NXNmhyODdaZlZvcGQiLCJtYWMiOiI0MWJiMzY1M2Y1NWQ5NGJkMGY5ZTdiNjE5MDg0MGE1MmMxODliZGM3N2Q0YzNlMTgwMjQzYWQzNDZmMzBlZDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-550558662\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1844636724 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55465</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2031</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2031</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IitVcHNzQTQ2YUwrNGs4c0ZXcFZKS1E9PSIsInZhbHVlIjoiRUppYUhualpMMTJpTVhDWTF5akxWNjdKRW4raWNzRld2c2ZKNExhRFBOM2N5UitPNXBvdVpNSHJXSU0wWVdVdFl5REJRVjJEVmFhS21GMXFTMjYxMU9ZcGp2djlwQmlZL3JOdDh0WlBXT1BVUWFhNFB2VmN4RmN4VmQwbVlBRGsiLCJtYWMiOiJjZTc2YTQ0ZmQ2NWJlMDIyNWZiY2FmMjM0ZGE4NjdlMDhhODlmYTZkZmIyY2U3N2FmMTZmZjJmMWY5N2Y4MzNkIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6InJoNUFZRHFmR1ZySTAvK0xRQ3QrSnc9PSIsInZhbHVlIjoidGV3bEZqWGZMYVlFUXZpZ3JkOTFVRXdLeVpXdTJZY0hZRFMrdk9iWXlqSkRMRGxTQWh0WTBEZVdmWUI0U2kvd3Z0WWhxR0UveVRlSGVQVk9uQzQweXI0ZEwxcGdCSWRWMXJ3cWtHUDlsUCtNdkhNNEFKK2NXNmhyODdaZlZvcGQiLCJtYWMiOiI0MWJiMzY1M2Y1NWQ5NGJkMGY5ZTdiNjE5MDg0MGE1MmMxODliZGM3N2Q0YzNlMTgwMjQzYWQzNDZmMzBlZDkzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1758092262.9485</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1758092262</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844636724\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1451343872 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5YQflWm2dljwYG047t9akNiy9aKBn7vbP7mVMnSN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1451343872\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1045390354 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 17 Sep 2025 06:57:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InZIYjA0U0VuOUJOQytEVEp0RnNmR0E9PSIsInZhbHVlIjoiamF1WEp3WEdaRkw1TFViT0RHeG82ai9JeXdveXBvSy9pdnV6bFNzNE43WW96M2Zib0QzUllseWdKaWZJa2xPWVpFSm53emlCWGhCR21FZ1hMTys3d2Y4NlprdTFWVkxIY1o3d3VaTUtqMGdJRUg4bzd0VmUvWVNUcG5wQ21Od2siLCJtYWMiOiI5OGFhNmU5N2JmNzA1ZmQwMjBkYTUxOTY2NzU4MDVmYzk1ZGU3MDIzNjBjNGNiMzU4ZjBhOWU3MGE2NWVlNmY0IiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 08:57:43 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IkJ1ZUd6MjZUa3h0VUN0ZzAxR3ZxQkE9PSIsInZhbHVlIjoiRktiR1BHUXpIL2NEaFVJdjRJUS9panYvMXhJVnpVZEdRc2NwaEhkN1dHS044by9LMTVsZnYwbjZUZFYrd0JKYVdJT0cvQnArSG5mdlpJcTRzVDJKYmtGT2pBYSs1dTlLaHdoeUlmc2YyaTdEQ1pUZGRmR24yM1lrWWZ1QWVnZEQiLCJtYWMiOiIyNDY5YWFlYTA3Yjg0MjNhNzE1OGVhNzU3MjEwZjA1MGZhODMwN2MzYWVkNDk0NmI5MjljZmJkYzY1MTc5ODIzIiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 08:57:43 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InZIYjA0U0VuOUJOQytEVEp0RnNmR0E9PSIsInZhbHVlIjoiamF1WEp3WEdaRkw1TFViT0RHeG82ai9JeXdveXBvSy9pdnV6bFNzNE43WW96M2Zib0QzUllseWdKaWZJa2xPWVpFSm53emlCWGhCR21FZ1hMTys3d2Y4NlprdTFWVkxIY1o3d3VaTUtqMGdJRUg4bzd0VmUvWVNUcG5wQ21Od2siLCJtYWMiOiI5OGFhNmU5N2JmNzA1ZmQwMjBkYTUxOTY2NzU4MDVmYzk1ZGU3MDIzNjBjNGNiMzU4ZjBhOWU3MGE2NWVlNmY0IiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 08:57:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IkJ1ZUd6MjZUa3h0VUN0ZzAxR3ZxQkE9PSIsInZhbHVlIjoiRktiR1BHUXpIL2NEaFVJdjRJUS9panYvMXhJVnpVZEdRc2NwaEhkN1dHS044by9LMTVsZnYwbjZUZFYrd0JKYVdJT0cvQnArSG5mdlpJcTRzVDJKYmtGT2pBYSs1dTlLaHdoeUlmc2YyaTdEQ1pUZGRmR24yM1lrWWZ1QWVnZEQiLCJtYWMiOiIyNDY5YWFlYTA3Yjg0MjNhNzE1OGVhNzU3MjEwZjA1MGZhODMwN2MzYWVkNDk0NmI5MjljZmJkYzY1MTc5ODIzIiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 08:57:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045390354\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1362309144 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1758092143</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362309144\", {\"maxDepth\":0})</script>\n"}}