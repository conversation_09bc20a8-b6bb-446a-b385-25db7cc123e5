{"__meta": {"id": "X0f34d871cc70d7066390a06ddaf1a7c9", "datetime": "2025-09-17 09:56:26", "utime": 1758092186.307595, "method": "POST", "uri": "/livewire/message/ues", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758092185.469617, "end": 1758092186.307617, "duration": 0.8380000591278076, "duration_str": "838ms", "measures": [{"label": "Booting", "start": 1758092185.469617, "relative_start": 0, "end": 1758092186.089687, "relative_end": 1758092186.089687, "duration": 0.620070219039917, "duration_str": "620ms", "params": [], "collector": null}, {"label": "Application", "start": 1758092186.090355, "relative_start": 0.6207380294799805, "end": 1758092186.307619, "relative_end": 2.1457672119140625e-06, "duration": 0.21726417541503906, "duration_str": "217ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 27249728, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "livewire.deraq.ue.index (\\resources\\views\\livewire\\deraq\\ue\\index.blade.php)", "param_count": 40, "params": ["ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "errors", "_instance", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/index.blade.php&line=0"}, {"name": "livewire.deraq.ue.liste (\\resources\\views\\livewire\\deraq\\ue\\liste.blade.php)", "param_count": 42, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}, {"name": "livewire.deraq.ue.duplication-modal (\\resources\\views\\livewire\\deraq\\ue\\duplication-modal.blade.php)", "param_count": 48, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators", "__currentLoopData", "parcour", "loop", "niveau", "annee", "ue"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/duplication-modal.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.01694, "accumulated_duration_str": "16.94ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00417, "duration_str": "4.17ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 24.616}, {"sql": "select count(*) as aggregate from `ues` where `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00107, "duration_str": "1.07ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 24.616, "width_percent": 6.316}, {"sql": "select * from `ues` where `ues`.`deleted_at` is null limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 30.933, "width_percent": 4.132}, {"sql": "select * from `parcours` where `parcours`.`id` in (2, 4, 5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00186, "duration_str": "1.86ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 35.065, "width_percent": 10.98}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 46.045, "width_percent": 3.719}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 49.764, "width_percent": 3.837}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 53.601, "width_percent": 4.309}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (80, 84, 85, 87, 88, 89, 90, 91, 92, 93) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 57.91, "width_percent": 5.962}, {"sql": "select * from `users` where `users`.`id` in (145, 147, 148, 149, 151, 155, 156, 157, 158, 159, 177, 180) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 107}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0012900000000000001, "duration_str": "1.29ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:107", "connection": "imsaaapp", "start_percent": 63.872, "width_percent": 7.615}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 108}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:108", "connection": "imsaaapp", "start_percent": 71.488, "width_percent": 5.195}, {"sql": "select * from `semestres` where `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 109}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:109", "connection": "imsaaapp", "start_percent": 76.682, "width_percent": 4.545}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 110}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:110", "connection": "imsaaapp", "start_percent": 81.228, "width_percent": 4.073}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 111}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:111", "connection": "imsaaapp", "start_percent": 85.301, "width_percent": 4.545}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 2) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 112}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00172, "duration_str": "1.72ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:112", "connection": "imsaaapp", "start_percent": 89.847, "width_percent": 10.153}]}, "models": {"data": {"App\\Models\\Matiere": 21, "App\\Models\\AnneeUniversitaire": 8, "App\\Models\\Semestre": 11, "App\\Models\\Niveau": 6, "App\\Models\\Parcour": 27, "App\\Models\\Ue": 10, "App\\Models\\User": 64}, "count": 147}, "livewire": {"data": {"ues #On9UEMExUvwvwtFDmk4t": "array:5 [\n  \"data\" => array:30 [\n    \"currentPage\" => \"liste\"\n    \"query\" => \"\"\n    \"selectedParcours\" => []\n    \"selectedNiveaux\" => []\n    \"filtreAnnee\" => \"\"\n    \"newUe\" => []\n    \"editUe\" => []\n    \"editingUeId\" => null\n    \"expandedUes\" => []\n    \"showQuickAddModal\" => false\n    \"showEcModal\" => false\n    \"ecModalMode\" => \"add\"\n    \"ecForm\" => []\n    \"currentEcUe\" => null\n    \"currentEcId\" => null\n    \"showDuplicationModal\" => true\n    \"duplicationStep\" => \"selection\"\n    \"selectedUesForDuplication\" => []\n    \"targetParcours\" => []\n    \"targetAnneeId\" => \"1\"\n    \"duplicationPreview\" => []\n    \"editablePreviewData\" => []\n    \"duplicationResults\" => []\n    \"enseignantQuery\" => \"\"\n    \"filteredEnseignants\" => []\n    \"showAddEnseignantForm\" => false\n    \"newEnseignant\" => array:5 [\n      \"nom\" => \"\"\n      \"prenom\" => \"\"\n      \"email\" => \"\"\n      \"telephone1\" => \"\"\n      \"sexe\" => \"M\"\n    ]\n    \"recentlyAssignedEnseignants\" => []\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"ues\"\n  \"view\" => \"livewire.deraq.ue.index\"\n  \"component\" => \"App\\Http\\Livewire\\Ues\"\n  \"id\" => \"On9UEMExUvwvwtFDmk4t\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pedagogiques/ue\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1758092143\n]"}, "request": {"path_info": "/livewire/message/ues", "status_code": "<pre class=sf-dump id=sf-dump-1436043817 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1436043817\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1965699247 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1965699247\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1094444352 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">On9UEMExUvwvwtFDmk4t</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ues</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">pedagogiques/ue</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dc7d8478</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:30</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>selectedParcours</span>\" => []\n      \"<span class=sf-dump-key>selectedNiveaux</span>\" => []\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => \"\"\n      \"<span class=sf-dump-key>newUe</span>\" => []\n      \"<span class=sf-dump-key>editUe</span>\" => []\n      \"<span class=sf-dump-key>editingUeId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>expandedUes</span>\" => []\n      \"<span class=sf-dump-key>showQuickAddModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEcModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>ecModalMode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">add</span>\"\n      \"<span class=sf-dump-key>ecForm</span>\" => []\n      \"<span class=sf-dump-key>currentEcUe</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentEcId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>showDuplicationModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>duplicationStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">selection</span>\"\n      \"<span class=sf-dump-key>selectedUesForDuplication</span>\" => []\n      \"<span class=sf-dump-key>targetParcours</span>\" => []\n      \"<span class=sf-dump-key>targetAnneeId</span>\" => \"\"\n      \"<span class=sf-dump-key>duplicationPreview</span>\" => []\n      \"<span class=sf-dump-key>editablePreviewData</span>\" => []\n      \"<span class=sf-dump-key>duplicationResults</span>\" => []\n      \"<span class=sf-dump-key>enseignantQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>filteredEnseignants</span>\" => []\n      \"<span class=sf-dump-key>showAddEnseignantForm</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>newEnseignant</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>nom</span>\" => \"\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"\"\n        \"<span class=sf-dump-key>email</span>\" => \"\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"\"\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>M</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>recentlyAssignedEnseignants</span>\" => []\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">2493e86d857fab7045412a312395e4d11c8241281fd63af05ade3116f843891d</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">syncInput</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">h7jd</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">targetAnneeId</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094444352\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1197201417 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1066</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjdXc2g3aEZLOHRJQzkxandBS3JXSmc9PSIsInZhbHVlIjoiRXV0STZqMDF6YnBLUWxQaHpsVTlvU3BqSlcvNXJQWkIxSFlNTnhUOUh0TlM4QVpJSDJqcjljQWpDV0p4M3l5azFSb1hCNENXT1ByMlFLaFo1UEtzZHFFemd0MWNVOGVzdHdUc21yb3V6Mlp0clo5QkxEOVVneldVdE9LcGFWclYiLCJtYWMiOiIzNWNmYmNlMzY1YTM1ZjIwODZiOGI4NTU3YjQ2YWQ4ZGU1ZjZlMjM5ODVkMmU1ZTI4ZGNlYWY0YmJjMGZiZTY5IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlhWMHpERGRWV3I5Q0tKVElneW5Ka2c9PSIsInZhbHVlIjoiYkVMczRJQTdHMEtrbDRpUDQwYmZObWU0dXh4d29YcUQ5azVTOW12OGFXaFk0bXo5V3BITEkxZUoyVThiOTlOWW1nSnBvcEFtUXM0R1BoZDYwL3VkSElUL1VlWVBoTWE3S3poaEYvemVEY1NoNjd0QzZKS2N5OGk0VU1ERmNiMjciLCJtYWMiOiJkZGVkMmVjODA4ZTQ4NzJkMGI4YjVlZmNlMGE3NjZkOGZkOTY3Yjg1YmM1YWY2YmI5ZGNlMGVhNzQ3Y2ZmYTNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1197201417\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-945434571 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55371</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1066</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1066</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjdXc2g3aEZLOHRJQzkxandBS3JXSmc9PSIsInZhbHVlIjoiRXV0STZqMDF6YnBLUWxQaHpsVTlvU3BqSlcvNXJQWkIxSFlNTnhUOUh0TlM4QVpJSDJqcjljQWpDV0p4M3l5azFSb1hCNENXT1ByMlFLaFo1UEtzZHFFemd0MWNVOGVzdHdUc21yb3V6Mlp0clo5QkxEOVVneldVdE9LcGFWclYiLCJtYWMiOiIzNWNmYmNlMzY1YTM1ZjIwODZiOGI4NTU3YjQ2YWQ4ZGU1ZjZlMjM5ODVkMmU1ZTI4ZGNlYWY0YmJjMGZiZTY5IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlhWMHpERGRWV3I5Q0tKVElneW5Ka2c9PSIsInZhbHVlIjoiYkVMczRJQTdHMEtrbDRpUDQwYmZObWU0dXh4d29YcUQ5azVTOW12OGFXaFk0bXo5V3BITEkxZUoyVThiOTlOWW1nSnBvcEFtUXM0R1BoZDYwL3VkSElUL1VlWVBoTWE3S3poaEYvemVEY1NoNjd0QzZKS2N5OGk0VU1ERmNiMjciLCJtYWMiOiJkZGVkMmVjODA4ZTQ4NzJkMGI4YjVlZmNlMGE3NjZkOGZkOTY3Yjg1YmM1YWY2YmI5ZGNlMGVhNzQ3Y2ZmYTNmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1758092185.4696</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1758092185</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-945434571\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1303706718 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5YQflWm2dljwYG047t9akNiy9aKBn7vbP7mVMnSN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303706718\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1965885795 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 17 Sep 2025 06:56:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImtSRkhmcGtYNUFsRVZaczRSK0UwWUE9PSIsInZhbHVlIjoiemZxTXAvRG9SMVdadlQ4SDNla2xaKzJ6dTV0aFI0MXVlejFjanBDR1dacXEvR3VmZ2N4NzlwNXFGYW50Rk1NZ1dFZGdmbWxad01iVHcvMG1vUjdPdW9SNWwwaE02R3JBUnRrSDE0U08wY2EwQ1Y2N2VwUGFER1pyR2oyWTBFQ2wiLCJtYWMiOiI0YTE2YTY3YjllZTQ4MmY3NWVmOTAyNTljMzFiODc0ZTA2NzU4YTQ5NjlhNTdiNzAwY2Q0NmI5ODBhNzE0OTA0IiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 08:56:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6Iit3U2ZycTVQYVp5aVZYVjRZK0ZHeXc9PSIsInZhbHVlIjoia0VRelZlWDBJQkk1dkdyRzdVeko1aldLOWp1Z3QrQWNTdXI2TXZ4ZlV3UGRrNHJEaGpyUlFTaldzZ3lxNnhkeVVGaUpzUEVmYWpPbXROSU5ZQzdRb3ErNHZvMGp2NDZrT0t3RDRIazlxT2FPdHBlWTE1d25kMDNaTXhheERVeGMiLCJtYWMiOiIxNTljZjVlOTBmMzJlZTEzMGFjZDA1OWJjMGI4NTQ3MjBlNjc2MDViNDliOGZkZTlhODZhMGVkODg2ZGM5NjJiIiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 08:56:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImtSRkhmcGtYNUFsRVZaczRSK0UwWUE9PSIsInZhbHVlIjoiemZxTXAvRG9SMVdadlQ4SDNla2xaKzJ6dTV0aFI0MXVlejFjanBDR1dacXEvR3VmZ2N4NzlwNXFGYW50Rk1NZ1dFZGdmbWxad01iVHcvMG1vUjdPdW9SNWwwaE02R3JBUnRrSDE0U08wY2EwQ1Y2N2VwUGFER1pyR2oyWTBFQ2wiLCJtYWMiOiI0YTE2YTY3YjllZTQ4MmY3NWVmOTAyNTljMzFiODc0ZTA2NzU4YTQ5NjlhNTdiNzAwY2Q0NmI5ODBhNzE0OTA0IiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 08:56:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6Iit3U2ZycTVQYVp5aVZYVjRZK0ZHeXc9PSIsInZhbHVlIjoia0VRelZlWDBJQkk1dkdyRzdVeko1aldLOWp1Z3QrQWNTdXI2TXZ4ZlV3UGRrNHJEaGpyUlFTaldzZ3lxNnhkeVVGaUpzUEVmYWpPbXROSU5ZQzdRb3ErNHZvMGp2NDZrT0t3RDRIazlxT2FPdHBlWTE1d25kMDNaTXhheERVeGMiLCJtYWMiOiIxNTljZjVlOTBmMzJlZTEzMGFjZDA1OWJjMGI4NTQ3MjBlNjc2MDViNDliOGZkZTlhODZhMGVkODg2ZGM5NjJiIiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 08:56:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1965885795\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2142704794 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1758092143</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2142704794\", {\"maxDepth\":0})</script>\n"}}