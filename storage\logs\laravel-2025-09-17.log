[2025-09-17 09:57:45] local.INFO: Detected N+1 Query  
[2025-09-17 09:57:45] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 3
Call-Stack:
#19 \app\Http\Livewire\Ues.php:520
#20 \app\Http\Livewire\Ues.php:507
#21 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#22 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#23 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#25 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#26 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#27 \vendor\livewire\livewire\src\LifecycleManager.php:89
#28 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#29 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#30 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#31 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#32 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#33 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#34 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#35 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#36 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#37 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#39 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#41 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#42 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#47 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
#49 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:797
  
[2025-09-17 09:57:45] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 3
Call-Stack:
#19 \app\Http\Livewire\Ues.php:520
#20 \app\Http\Livewire\Ues.php:507
#21 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#22 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#23 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#25 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#26 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#27 \vendor\livewire\livewire\src\LifecycleManager.php:89
#28 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#29 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#30 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#31 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#32 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#33 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#34 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#35 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#36 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#37 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#39 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#41 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#42 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#47 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
#49 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:797
  
[2025-09-17 09:57:45] local.INFO: Model: App\Models\Matiere
Relation: App\Models\User
Num-Called: 2
Call-Stack:
#24 \app\Http\Livewire\Ues.php:520
#25 \app\Http\Livewire\Ues.php:507
#26 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#27 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#28 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#29 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#30 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#31 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#32 \vendor\livewire\livewire\src\LifecycleManager.php:89
#33 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#34 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#35 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#37 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#38 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#40 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#47 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
  
[2025-09-17 10:04:38] local.INFO: Detected N+1 Query  
[2025-09-17 10:04:38] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:04:38] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:04:38] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:04:38] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:04:38] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:05] local.INFO: Detected N+1 Query  
[2025-09-17 10:05:05] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:05] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:05] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:05] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:05] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:13] local.INFO: Detected N+1 Query  
[2025-09-17 10:05:13] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:13] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:13] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:13] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:13] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:20] local.INFO: Detected N+1 Query  
[2025-09-17 10:05:20] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:20] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:20] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:20] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:05:20] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:06:17] local.INFO: Detected N+1 Query  
[2025-09-17 10:06:17] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:06:17] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:06:17] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:06:17] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:06:17] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:06:28] local.INFO: Detected N+1 Query  
[2025-09-17 10:06:28] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:06:28] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:06:28] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:06:28] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:06:28] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:45] local.INFO: Detected N+1 Query  
[2025-09-17 10:08:45] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:45] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:45] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:45] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:45] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:47] local.INFO: Detected N+1 Query  
[2025-09-17 10:08:47] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:47] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:47] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:47] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:47] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:53] local.INFO: Detected N+1 Query  
[2025-09-17 10:08:53] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:53] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:53] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:53] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:53] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:57] local.INFO: Detected N+1 Query  
[2025-09-17 10:08:57] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:57] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:57] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:57] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:08:57] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:03] local.INFO: Detected N+1 Query  
[2025-09-17 10:09:03] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:03] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:03] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:03] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:03] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:05] local.INFO: Detected N+1 Query  
[2025-09-17 10:09:05] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:05] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:05] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:05] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:05] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 3
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:12] local.INFO: Detected N+1 Query  
[2025-09-17 10:09:12] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:12] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:12] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:12] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:12] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:18] local.INFO: Detected N+1 Query  
[2025-09-17 10:09:18] local.INFO: Model: App\Models\Ue
Relation: App\Models\Parcour
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:18] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:18] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:18] local.INFO: Model: App\Models\Ue
Relation: App\Models\AnneeUniversitaire
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:18] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 2
Call-Stack:
#17 \app\Http\Livewire\Ues.php:567
#18 \storage\framework\views\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100
#20 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#21 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#22 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#23 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#24 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#25 \storage\framework\views\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667
#27 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#28 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#29 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#30 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#31 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#32 \storage\framework\views\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18
#34 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:84
#35 \vendor\livewire\livewire\src\ComponentConcerns\RendersLivewireComponents.php:59
#36 \vendor\laravel\framework\src\Illuminate\View\View.php:195
#37 \vendor\laravel\framework\src\Illuminate\View\View.php:178
#38 \vendor\laravel\framework\src\Illuminate\View\View.php:147
#39 \vendor\livewire\livewire\src\Component.php:235
#40 \vendor\livewire\livewire\src\HydrationMiddleware\RenderView.php:14
#41 \vendor\livewire\livewire\src\LifecycleManager.php:154
#42 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:15
#43 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#44 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#45 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#46 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#49 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
  
[2025-09-17 10:09:22] local.INFO: Detected N+1 Query  
[2025-09-17 10:09:22] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 2
Call-Stack:
#19 \app\Http\Livewire\Ues.php:640
#20 \app\Http\Livewire\Ues.php:627
#21 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#22 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#23 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#25 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#26 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#27 \vendor\livewire\livewire\src\LifecycleManager.php:89
#28 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#29 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#30 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#31 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#32 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#33 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#34 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#35 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#36 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#37 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#39 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#41 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#42 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#47 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
#49 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:797
  
[2025-09-17 10:09:22] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 2
Call-Stack:
#19 \app\Http\Livewire\Ues.php:640
#20 \app\Http\Livewire\Ues.php:627
#21 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#22 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#23 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#25 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#26 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#27 \vendor\livewire\livewire\src\LifecycleManager.php:89
#28 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#29 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#30 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#31 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#32 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#33 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#34 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#35 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#36 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#37 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#39 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#41 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#42 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#47 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
#49 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:797
  
