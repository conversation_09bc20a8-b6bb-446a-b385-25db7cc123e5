[2025-09-17 09:57:45] local.INFO: Detected N+1 Query  
[2025-09-17 09:57:45] local.INFO: Model: App\Models\Ue
Relation: App\Models\Niveau
Num-Called: 3
Call-Stack:
#19 \app\Http\Livewire\Ues.php:520
#20 \app\Http\Livewire\Ues.php:507
#21 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#22 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#23 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#25 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#26 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#27 \vendor\livewire\livewire\src\LifecycleManager.php:89
#28 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#29 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#30 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#31 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#32 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#33 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#34 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#35 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#36 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#37 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#39 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#41 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#42 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#47 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
#49 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:797
  
[2025-09-17 09:57:45] local.INFO: Model: App\Models\Ue
Relation: App\Models\Semestre
Num-Called: 3
Call-Stack:
#19 \app\Http\Livewire\Ues.php:520
#20 \app\Http\Livewire\Ues.php:507
#21 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#22 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#23 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#25 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#26 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#27 \vendor\livewire\livewire\src\LifecycleManager.php:89
#28 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#29 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#30 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#31 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#32 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#33 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#34 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#35 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#36 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#37 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#39 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#40 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#41 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#42 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#47 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
#49 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:797
  
[2025-09-17 09:57:45] local.INFO: Model: App\Models\Matiere
Relation: App\Models\User
Num-Called: 2
Call-Stack:
#24 \app\Http\Livewire\Ues.php:520
#25 \app\Http\Livewire\Ues.php:507
#26 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#27 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#28 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#29 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#30 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#31 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#32 \vendor\livewire\livewire\src\LifecycleManager.php:89
#33 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#34 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#35 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#36 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#37 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#38 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#40 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#47 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
  
