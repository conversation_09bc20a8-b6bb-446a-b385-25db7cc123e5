<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Ue extends Model
{
    use HasFactory, SoftDeletes;

    public $timestamps = false;

    protected $fillable = [
        'nom',
        'code',
        'credit',
        'parcour_id',
        'niveau_id',
        'semestre_id',
        'annee_universitaire_id',
        
    ];

    public function matiere(){
        return $this->hasMany(Matiere::class);
    }

    public function semestre(){
        return $this->belongsTo(Semestre::class);
    }

    public function niveau(){
        return $this->belongsTo(Niveau::class);
    }

    public function annee(){
        return $this->belongsTo(AnneeUniversitaire::class, "annee_universitaire_id", "id");
    }

    public function parcours(){
        return $this->belongsTo(Parcour::class, "parcour_id", "id");
    }

    public function note(){
        return $this->hasManyThrough(Note::class, Matiere::class);
    }

    /**
     * Duplicate this UE to multiple parcours for a specific academic year
     *
     * @param array $targetParcourIds Array of parcour IDs to duplicate to
     * @param int $targetAnneeId Target academic year ID
     * @param array $ueData Optional UE data modifications
     * @param array $ecData Optional EC data modifications (keyed by original EC ID)
     * @return array Array of created UE IDs
     */
    public function duplicateToMultipleParcours($targetParcourIds, $targetAnneeId, $ueData = [], $ecData = [])
    {
        $createdUeIds = [];

        foreach ($targetParcourIds as $parcourId) {
            $duplicatedUe = $this->duplicateToParcours($parcourId, $targetAnneeId, $ueData, $ecData);
            if ($duplicatedUe) {
                $createdUeIds[] = $duplicatedUe->id;
            }
        }

        return $createdUeIds;
    }

    /**
     * Duplicate this UE to a specific parcours for a specific academic year
     *
     * @param int $targetParcourId Target parcour ID
     * @param int $targetAnneeId Target academic year ID
     * @param array $ueData Optional UE data modifications
     * @param array $ecData Optional EC data modifications (keyed by original EC ID)
     * @return Ue|null The created UE or null if failed
     */
    public function duplicateToParcours($targetParcourId, $targetAnneeId, $ueData = [], $ecData = [])
    {
        // Check if UE already exists for this parcours/year combination
        $existingUe = static::where('code', $this->code)
            ->where('parcour_id', $targetParcourId)
            ->where('annee_universitaire_id', $targetAnneeId)
            ->first();

        if ($existingUe) {
            // Skip if already exists
            return null;
        }

        // Use custom UE data if provided, otherwise use original data
        $finalUeData = array_merge([
            'nom' => $this->nom,
            'code' => $this->code,
            'credit' => $this->credit,
            'niveau_id' => $this->niveau_id,
            'semestre_id' => $this->semestre_id,
            'parcour_id' => $targetParcourId,
            'annee_universitaire_id' => $targetAnneeId,
        ], $ueData);

        // Create the new UE
        $newUe = static::create($finalUeData);

        // Duplicate associated ECs (Matieres)
        foreach ($this->matiere as $originalEc) {
            $this->duplicateEc($originalEc, $newUe, $ecData);
        }

        return $newUe;
    }

    /**
     * Duplicate an EC (Matiere) to a new UE
     *
     * @param Matiere $originalEc Original EC to duplicate
     * @param Ue $targetUe Target UE to attach the EC to
     * @param array $ecData Optional EC data modifications (keyed by original EC ID)
     * @return Matiere|null The created EC or null if failed
     */
    private function duplicateEc($originalEc, $targetUe, $ecData = [])
    {
        // Generate unique code for the new EC
        $baseCode = $originalEc->code;
        $newCode = $this->generateUniqueEcCode($baseCode, $targetUe);

        // Check if EC already exists for this UE with the new code
        $existingEc = $targetUe->matiere()->where('code', $newCode)->first();

        if ($existingEc) {
            // Skip if already exists
            return null;
        }

        // Get any custom data for this specific EC
        $customEcData = $ecData[$originalEc->id] ?? [];

        // Prepare EC data for duplication
        $newEcData = array_merge([
            'nom' => $originalEc->nom,
            'code' => $newCode,
            'syllabus' => $originalEc->syllabus,
            'user_id' => $originalEc->user_id, // Keep the same teacher assignment
            'ue_id' => $targetUe->id,
        ], $customEcData);

        // Create the new EC
        return \App\Models\Matiere::create($newEcData);
    }

    /**
     * Generate a unique EC code based on the original code and target UE
     *
     * @param string $baseCode Original EC code
     * @param Ue $targetUe Target UE
     * @return string Unique EC code
     */
    private function generateUniqueEcCode($baseCode, $targetUe)
    {
        // Try the original code first
        if (!\App\Models\Matiere::where('code', $baseCode)->exists()) {
            return $baseCode;
        }

        // If original code exists, append parcours sigle
        $parcoursSigle = $targetUe->parcours->sigle ?? 'UNK';
        $newCode = $baseCode . '_' . $parcoursSigle;

        if (!\App\Models\Matiere::where('code', $newCode)->exists()) {
            return $newCode;
        }

        // If that exists too, append a counter
        $counter = 1;
        do {
            $newCode = $baseCode . '_' . $parcoursSigle . '_' . $counter;
            $counter++;
        } while (\App\Models\Matiere::where('code', $newCode)->exists() && $counter < 100);

        return $newCode;
    }

    /**
     * Get preview data for duplication
     *
     * @param array $targetParcourIds Array of parcour IDs to duplicate to
     * @param int $targetAnneeId Target academic year ID
     * @return array Preview data structure
     */
    public function getDuplicationPreview($targetParcourIds, $targetAnneeId)
    {
        $preview = [
            'ue' => [
                'id' => $this->id,
                'nom' => $this->nom,
                'code' => $this->code,
                'credit' => $this->credit,
                'niveau_id' => $this->niveau_id,
                'semestre_id' => $this->semestre_id,
                'niveau_nom' => $this->niveau->nom,
                'semestre_nom' => $this->semestre->nom,
            ],
            'ecs' => [],
            'target_parcours' => [],
            'conflicts' => []
        ];

        // Get EC data
        foreach ($this->matiere as $ec) {
            $preview['ecs'][] = [
                'id' => $ec->id,
                'nom' => $ec->nom,
                'code' => $ec->code,
                'syllabus' => $ec->syllabus,
                'user_id' => $ec->user_id,
                'enseignant_nom' => $ec->user ? $ec->user->nom . ' ' . $ec->user->prenom : null,
            ];
        }

        // Get target parcours data and check for conflicts
        foreach ($targetParcourIds as $parcourId) {
            $parcours = \App\Models\Parcour::find($parcourId);
            if ($parcours) {
                $preview['target_parcours'][] = [
                    'id' => $parcours->id,
                    'nom' => $parcours->nom,
                    'sigle' => $parcours->sigle,
                ];

                // Check for existing UE conflicts
                $existingUe = static::where('code', $this->code)
                    ->where('parcour_id', $parcourId)
                    ->where('annee_universitaire_id', $targetAnneeId)
                    ->first();

                if ($existingUe) {
                    $preview['conflicts'][] = [
                        'type' => 'ue',
                        'parcours_id' => $parcourId,
                        'parcours_nom' => $parcours->nom,
                        'message' => "UE '{$this->code}' existe déjà pour ce parcours"
                    ];
                }
            }
        }

        return $preview;
    }

}
