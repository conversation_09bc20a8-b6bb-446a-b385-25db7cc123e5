{"__meta": {"id": "X5a540fa8bb4e3f442dc912c227e02314", "datetime": "2025-09-17 09:55:35", "utime": 1758092135.562013, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758092134.880472, "end": 1758092135.562042, "duration": 0.6815700531005859, "duration_str": "682ms", "measures": [{"label": "Booting", "start": 1758092134.880472, "relative_start": 0, "end": 1758092135.437068, "relative_end": 1758092135.437068, "duration": 0.556596040725708, "duration_str": "557ms", "params": [], "collector": null}, {"label": "Application", "start": 1758092135.438, "relative_start": 0.557528018951416, "end": 1758092135.562045, "relative_end": 3.0994415283203125e-06, "duration": 0.12404513359069824, "duration_str": "124ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 23619680, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "auth.login (\\resources\\views\\auth\\login.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/auth/login.blade.php&line=0"}, {"name": "layouts.simple (\\resources\\views\\layouts\\simple.blade.php)", "param_count": 3, "params": ["__env", "app", "errors"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/layouts/simple.blade.php&line=0"}]}, "route": {"uri": "GET login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=19\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:19-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FQGXsORlAZA49O9xmSbviYnoTo8dq0crhxZ38TJF", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1424016882 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1424016882\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1021655158 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1021655158\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2116261504 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2116261504\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-966150515 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik5hTjhYVU9zT2pYaTZ4UVBKWHJ0a0E9PSIsInZhbHVlIjoiMEZnYWlXSkVoeS9ucFVjTnVnL2RONkViamJBZjlxa2tnRlJzZHVyUXM0WjRETzdGN1J2aHZibnRJVUNEMVpBenVSa1FCT2xlaEpFK1NEK0NiZkRTR1lwN2NIUmF0YWRuQjE5eFRjSFdtbFJSdjl4clFnRDlyRzBrWXhzOStTQjgiLCJtYWMiOiJiMWFiZGMxM2Y2NzUyNTJiNjBlMzIzMWZhN2YxMjFhYjlmZjkyMDZmZmY3YTZhZjVmMjYzMDYzN2VjN2UxMDZiIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjRFN0tNOGcrOHFoeWxWeitNWlJQRHc9PSIsInZhbHVlIjoiSXV4ckQ1RHlhQ3lpQXlReDJNUUx4enE0L25XYjhyK2dJZ3Y4eEZYYnlNSENSaStLa3c5cTV3WG5MTHlqaUtnQXRtZ29zODBUeWpKS092ZmFDdm8xUjkzdnJYOWJhcmNkNGFRaW5zWEtlT01jMnpQdkVyeHhyaUhXRDlieXA5QWciLCJtYWMiOiJmMGQwMDU0ZmYxZWFkZjQyZDBlMmU5YTA5N2JhYjgxZTI0MzVkMmU4ZTZlZGNkNmVhMjM2MmM0MDU1MTQ3NmMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966150515\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-137031642 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55276</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik5hTjhYVU9zT2pYaTZ4UVBKWHJ0a0E9PSIsInZhbHVlIjoiMEZnYWlXSkVoeS9ucFVjTnVnL2RONkViamJBZjlxa2tnRlJzZHVyUXM0WjRETzdGN1J2aHZibnRJVUNEMVpBenVSa1FCT2xlaEpFK1NEK0NiZkRTR1lwN2NIUmF0YWRuQjE5eFRjSFdtbFJSdjl4clFnRDlyRzBrWXhzOStTQjgiLCJtYWMiOiJiMWFiZGMxM2Y2NzUyNTJiNjBlMzIzMWZhN2YxMjFhYjlmZjkyMDZmZmY3YTZhZjVmMjYzMDYzN2VjN2UxMDZiIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjRFN0tNOGcrOHFoeWxWeitNWlJQRHc9PSIsInZhbHVlIjoiSXV4ckQ1RHlhQ3lpQXlReDJNUUx4enE0L25XYjhyK2dJZ3Y4eEZYYnlNSENSaStLa3c5cTV3WG5MTHlqaUtnQXRtZ29zODBUeWpKS092ZmFDdm8xUjkzdnJYOWJhcmNkNGFRaW5zWEtlT01jMnpQdkVyeHhyaUhXRDlieXA5QWciLCJtYWMiOiJmMGQwMDU0ZmYxZWFkZjQyZDBlMmU5YTA5N2JhYjgxZTI0MzVkMmU4ZTZlZGNkNmVhMjM2MmM0MDU1MTQ3NmMzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1758092134.8805</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1758092134</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-137031642\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-924649036 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FQGXsORlAZA49O9xmSbviYnoTo8dq0crhxZ38TJF</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PNcoOFiUKuJ9pHCjSl5YPxbGY3n5rzu0uppZ0YqC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-924649036\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1845984248 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 17 Sep 2025 06:55:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ijh1NFpaS0tra1g3ZkM2QjlDR3g4Tmc9PSIsInZhbHVlIjoialBqMXVIc21rZXEyMTFnZnNBUWgvb3BxUmdDalpuSmttZVF4ak5uSlc3SXdMMUtWUUNIUWtsYSsxckZNVXkxU0NoNEJBc01sdGJxMEduUytuczR6QVNzYUl4SldkNkR3bFRIMk5KbkRuZW0zMFVFaE9MNUxLWWJQRUpML28veW4iLCJtYWMiOiI4MmM0MjgwM2VhNmUyZTVkMmE1MmYzM2YxYTYxOGNkNzZjYjEzZjU1OTQ5OTZkZDM1MjM0OTcwMmQ1NTcwMDk3IiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 08:55:35 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IjYyNHFhU1ZnTkd2K3I2ZjlGZW4xL2c9PSIsInZhbHVlIjoiSm5uOGZ1UTA4QmhkQkRpM1JxRnE2VmFzTFRZd3BFSjFmbG9NVUV1Y215TldNQzFvVGQ1N0I0MW1nVlpiQUVLZkdwaUVjS04rRzdZdGRMTXRwN0pyQUg4c0JLVDlvVEZYdU96RWhYRURBSXoyeERKVS9ycGRkeGhNQ0Q3S3R4Z3giLCJtYWMiOiI5NjFmODk4MDA4NjVmNzdkNTQ4YTViN2Q1NDA1YTE5YTgxOWNmMzgzMGEwNjU1ZGJlNzgzYTMwMWUwMWVhODM1IiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 08:55:35 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ijh1NFpaS0tra1g3ZkM2QjlDR3g4Tmc9PSIsInZhbHVlIjoialBqMXVIc21rZXEyMTFnZnNBUWgvb3BxUmdDalpuSmttZVF4ak5uSlc3SXdMMUtWUUNIUWtsYSsxckZNVXkxU0NoNEJBc01sdGJxMEduUytuczR6QVNzYUl4SldkNkR3bFRIMk5KbkRuZW0zMFVFaE9MNUxLWWJQRUpML28veW4iLCJtYWMiOiI4MmM0MjgwM2VhNmUyZTVkMmE1MmYzM2YxYTYxOGNkNzZjYjEzZjU1OTQ5OTZkZDM1MjM0OTcwMmQ1NTcwMDk3IiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 08:55:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IjYyNHFhU1ZnTkd2K3I2ZjlGZW4xL2c9PSIsInZhbHVlIjoiSm5uOGZ1UTA4QmhkQkRpM1JxRnE2VmFzTFRZd3BFSjFmbG9NVUV1Y215TldNQzFvVGQ1N0I0MW1nVlpiQUVLZkdwaUVjS04rRzdZdGRMTXRwN0pyQUg4c0JLVDlvVEZYdU96RWhYRURBSXoyeERKVS9ycGRkeGhNQ0Q3S3R4Z3giLCJtYWMiOiI5NjFmODk4MDA4NjVmNzdkNTQ4YTViN2Q1NDA1YTE5YTgxOWNmMzgzMGEwNjU1ZGJlNzgzYTMwMWUwMWVhODM1IiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 08:55:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1845984248\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-778817877 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FQGXsORlAZA49O9xmSbviYnoTo8dq0crhxZ38TJF</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-778817877\", {\"maxDepth\":0})</script>\n"}}