<!-- Hero -->
<div class="bg-body-light">
    <div class="content content-full">
        <h1 class="h3 fw-bold mb-2">
            Gestion des UE
        </h1>
    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">
    <!-- Recherche unifiée et filtres rapides -->
    <div class="row mb-3">
        <div class="col-md-8">
            <div class="input-group">
                <input type="search" wire:model.debounce.300ms="query" class="form-control" 
                    placeholder="Rechercher par code, nom, parcours...">
                <button class="btn btn-alt-primary" type="button">
                    <i class="fa fa-search"></i>
                </button>
                <button class="btn btn-alt-secondary dropdown-toggle" type="button" id="dropdownFilters" 
                    data-bs-toggle="dropdown" aria-expanded="false">
                    Filtres <span class="badge bg-primary">{{ $activeFiltersCount }}</span>
                </button>
                <div class="dropdown-menu p-3" style="width: 300px" aria-labelledby="dropdownFilters">
                    <h6>Parcours</h6>
                    <div class="row g-2 mb-3">
                        @foreach ($parcours as $parcour)
                            <div class="col-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                        wire:model="selectedParcours" value="{{ $parcour->id }}" 
                                        id="parcours{{ $parcour->id }}">
                                    <label class="form-check-label" for="parcours{{ $parcour->id }}">
                                        {{ $parcour->sigle }}
                                    </label>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <h6>Niveau</h6>
                    <div class="row g-2 mb-3">
                        @foreach ($niveaux as $niveau)
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                        wire:model="selectedNiveaux" value="{{ $niveau->id }}" 
                                        id="niveau{{ $niveau->id }}">
                                    <label class="form-check-label" for="niveau{{ $niveau->id }}">
                                        {{ $niveau->nom }}
                                    </label>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <h6>Année</h6>
                    <select wire:model="filtreAnnee" class="form-select form-select-sm">
                        <option value="">Toutes les années</option>
                        @foreach ($annees as $annee)
                            <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group">
                <button class="btn btn-alt-info" wire:click="openDuplicationModal" title="Dupliquer des UE vers d'autres parcours">
                    <i class="fa fa-copy me-1"></i> Dupliquer UE
                </button>
                <button class="btn btn-primary" wire:click="$toggle('showQuickAddModal')">
                    <i class="fa fa-plus me-1"></i> Ajouter UE
                </button>
            </div>
        </div>
    </div>

    <!-- Liste des UE avec accordéon pour EC -->
    <div class="block block-rounded">
        <div class="block-content p-0">
            <div class="table-responsive">
                <table class="table table-striped table-vcenter mb-0">
                    <thead>
                        <tr>
                            <th style="width: 40px;"></th>
                            <th class="text-center" style="width: 100px;">Code</th>
                            <th>Nom</th>
                            <th style="width: 80px;">Crédit</th>
                            <th>Parcours & Niveau</th>
                            <th style="width: 120px;">Semestre</th>
                            <th style="width: 200px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($ues as $ue)
                            <tr>
                                <td class="text-center">
                                    <button class="btn btn-sm btn-alt-secondary" 
                                        wire:click="toggleEcList({{ $ue->id }})">
                                        <i class="fa fa-{{ in_array($ue->id, $expandedUes) ? 'minus' : 'plus' }}"></i>
                                    </button>
                                </td>
                                <td class="text-center fw-semibold">
                                    @if($editingUeId === $ue->id)
                                        <input type="text" wire:model.defer="editUe.code" class="form-control form-control-sm">
                                    @else
                                        {{ $ue->code }}
                                    @endif
                                </td>
                                <td class="fw-semibold">
                                    @if($editingUeId === $ue->id)
                                        <input type="text" wire:model.defer="editUe.nom" class="form-control form-control-sm">
                                    @else
                                        {{ $ue->nom }}
                                    @endif
                                </td>
                                <td>
                                    @if($editingUeId === $ue->id)
                                        <input type="number" wire:model.defer="editUe.credit" class="form-control form-control-sm">
                                    @else
                                        {{ $ue->credit }}
                                    @endif
                                </td>
                                <td>
                                    @if($editingUeId === $ue->id)
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <select wire:model.defer="editUe.parcour_id" class="form-select form-select-sm">
                                                    @foreach ($parcours as $parcour)
                                                        <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-6">
                                                <select wire:model.defer="editUe.niveau_id" class="form-select form-select-sm">
                                                    @foreach ($niveaux as $niveau)
                                                        <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    @else
                                        {{ $ue->parcours->sigle }} {{ $ue->niveau->nom }}
                                    @endif
                                </td>
                                <td>
                                    @if($editingUeId === $ue->id)
                                        <select wire:model.defer="editUe.semestre_id" class="form-select form-select-sm">
                                            @foreach ($semestres as $semestre)
                                                <option value="{{ $semestre->id }}">{{ $semestre->nom }}</option>
                                            @endforeach
                                        </select>
                                    @else
                                        {{ $ue->semestre->nom }}
                                    @endif
                                </td>
                                <td>
                                    @if($editingUeId === $ue->id)
                                        <div class="btn-group w-100">
                                            <button type="button" class="btn btn-sm btn-success" wire:click="updateUe">
                                                <i class="fa fa-check"></i> Enregistrer
                                            </button>
                                            <button type="button" class="btn btn-sm btn-alt-secondary" wire:click="cancelEdit">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                    @else
                                        <div class="btn-group">
                                            <button type="button" wire:click="startEditing({{ $ue->id }})"
                                                class="btn btn-sm btn-alt-secondary" title="Modifier UE">
                                                <i class="fa fa-pencil-alt"></i>
                                            </button>
                                            <button type="button" wire:click="addQuickEC({{ $ue->id }})"
                                                class="btn btn-sm btn-alt-primary" title="Ajouter EC">
                                                <i class="fa fa-plus"></i> EC
                                            </button>
                                            <button type="button" wire:click="deleteUe({{ $ue->id }})"
                                                class="btn btn-sm btn-alt-danger" title="Supprimer"
                                                onclick="confirm('Êtes-vous sûrs?') || event.stopImmediatePropagation()">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                    @endif
                                </td>
                            </tr>

                            <!-- Sous-tableau pour les ECs - Visible quand l'UE est expanded -->
                            @if(in_array($ue->id, $expandedUes))
                                <tr>
                                    <td colspan="7" class="p-0 border-0">
                                        <div class="bg-body-light p-3">
                                            <h6 class="mb-3">Eléments Constitutifs (EC) - {{ $ue->nom }}</h6>
                                            
                                            @if($ue->matiere->count() > 0)
                                                <table class="table table-sm table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th>Code</th>
                                                            <th>Nom</th>
                                                            <th>Enseignant</th>
                                                            <th style="width: 150px;">Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($ue->matiere as $matiere)
                                                            <tr>
                                                                <td>{{ $matiere->code }}</td>
                                                                <td>{{ $matiere->nom }}</td>
                                                                <td>
                                                                    @if ($matiere->user)
                                                                        {{ $matiere->user->nom }} {{ $matiere->user->prenom }}
                                                                    @else
                                                                        <span class="badge bg-warning">Non assigné</span>
                                                                    @endif
                                                                </td>
                                                                <td class="text-center">
                                                                    <div class="btn-group">
                                                                        <button type="button" wire:click="editEC({{ $matiere->id }})"
                                                                            class="btn btn-xs btn-alt-secondary">
                                                                            <i class="fa fa-pencil-alt"></i>
                                                                        </button>
                                                                        {{-- <button type="button" wire:click="assignEnseignant({{ $matiere->id }})"
                                                                            class="btn btn-xs btn-alt-info">
                                                                            <i class="fa fa-user"></i>
                                                                        </button> --}}
                                                                        <button type="button" wire:click="deleteEC({{ $matiere->id }})"
                                                                            class="btn btn-xs btn-alt-danger"
                                                                            onclick="confirm('Êtes-vous sûrs?') || event.stopImmediatePropagation()">
                                                                            <i class="fa fa-times"></i>
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            @else
                                                <div class="alert alert-info mb-0">
                                                    <i class="fa fa-info-circle me-1"></i> Aucun EC ajouté pour cette UE.
                                                    <button class="btn btn-sm btn-alt-info float-end" 
                                                        wire:click="addQuickEC({{ $ue->id }})">
                                                        <i class="fa fa-plus me-1"></i> Ajouter EC
                                                    </button>
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endif
                        @endforeach

                        @if($ues->count() == 0)
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">Aucune UE trouvée avec les critères actuels</div>
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
        <div class="block-content">
            <nav aria-label="Pagination">
                <ul class="pagination pagination-sm justify-content-end mt-2">
                    {{ $ues->links() }}
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Modal Ajout Rapide UE -->
@if($showQuickAddModal)
<div class="modal fade show d-block" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="block block-rounded block-transparent mb-0">
                <div class="block-header block-header-default">
                    <h3 class="block-title">Ajouter une UE</h3>
                    <div class="block-options">
                        <button type="button" class="btn-block-option" wire:click="$toggle('showQuickAddModal')">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="block-content">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Code</label>
                            <input type="text" class="form-control" wire:model.defer="newUe.code" placeholder="Code UE">
                            @error('newUe.code') <span class="text-danger">{{ $message }}</span> @enderror
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Crédit</label>
                            <input type="number" class="form-control" wire:model.defer="newUe.credit" placeholder="Nombre de crédits">
                            @error('newUe.credit') <span class="text-danger">{{ $message }}</span> @enderror
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Nom</label>
                        <input type="text" class="form-control" wire:model.defer="newUe.nom" placeholder="Nom de l'UE">
                        @error('newUe.nom') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Parcours</label>
                            <select class="form-select" wire:model.defer="newUe.parcour_id">
                                <option value="">Sélectionner un parcours</option>
                                @foreach ($parcours as $parcour)
                                    <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                @endforeach
                            </select>
                            @error('newUe.parcour_id') <span class="text-danger">{{ $message }}</span> @enderror
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Niveau</label>
                            <select class="form-select" wire:model.defer="newUe.niveau_id">
                                <option value="">Sélectionner un niveau</option>
                                @foreach ($niveaux as $niveau)
                                    <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                @endforeach
                            </select>
                            @error('newUe.niveau_id') <span class="text-danger">{{ $message }}</span> @enderror
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Semestre</label>
                            <select class="form-select" wire:model.defer="newUe.semestre_id">
                                <option value="">Sélectionner un semestre</option>
                                @foreach ($semestres as $semestre)
                                    <option value="{{ $semestre->id }}">{{ $semestre->nom }}</option>
                                @endforeach
                            </select>
                            @error('newUe.semestre_id') <span class="text-danger">{{ $message }}</span> @enderror
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Année</label>
                            <select class="form-select" wire:model.defer="newUe.annee_universitaire_id">
                                <option value="">Sélectionner une année</option>
                                @foreach ($annees as $annee)
                                    <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                @endforeach
                            </select>
                            @error('newUe.annee_universitaire_id') <span class="text-danger">{{ $message }}</span> @enderror
                        </div>
                    </div>
                </div>
                <div class="block-content block-content-full block-content-sm text-end border-top">
                    <button type="button" class="btn btn-alt-secondary" wire:click="$toggle('showQuickAddModal')">
                        Annuler
                    </button>
                    <button type="button" class="btn btn-primary" wire:click="addUe()">
                        <i class="fa fa-check opacity-50 me-1"></i> Ajouter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>
@endif


<!-- Modal Ajout/Édition Rapide EC -->
@if($showEcModal)
<div class="modal fade show d-block" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="block block-rounded block-transparent mb-0">
                <div class="block-header block-header-default">
                    <h3 class="block-title">{{ $ecModalMode === 'add' ? 'Ajouter' : 'Modifier' }} un EC</h3>
                    <div class="block-options">
                        <button type="button" class="btn-block-option" wire:click="closeEcModal">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="block-content">
                    <div class="row">
                        <!-- Colonne de gauche: Informations EC -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">UE</label>
                                <input type="text" class="form-control bg-gray-100" readonly 
                                    value="{{ $currentEcUe ? $currentEcUe->code . ' - ' . $currentEcUe->nom : '' }}">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Code EC</label>
                                <input type="text" class="form-control" wire:model.defer="ecForm.code" placeholder="Code EC">
                                @error('ecForm.code') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                            <div class="mb-4">
                                <label class="form-label">Nom EC</label>
                                <input type="text" class="form-control" wire:model.defer="ecForm.nom" placeholder="Nom de l'EC">
                                @error('ecForm.nom') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>
                        
                        <!-- Colonne de droite: Assignation Enseignant -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label class="form-label mb-0">Enseignant</label>
                                    <button type="button" class="btn btn-sm btn-alt-primary" wire:click="toggleAddEnseignantForm">
                                        <i class="fa fa-plus me-1"></i> Nouveau
                                    </button>
                                </div>
                                
                                <!-- Affiche l'enseignant actuellement sélectionné -->
                                @if(isset($ecForm['user_id']) && !empty($ecForm['user_id']))
                                    @php
                                        $selectedEnseignant = App\Models\User::find($ecForm['user_id']);
                                    @endphp
                                    @if($selectedEnseignant)
                                        <div class="alert alert-success p-2 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-grow-1">
                                                    <strong>{{ $selectedEnseignant->nom }} {{ $selectedEnseignant->prenom }}</strong>
                                                    <div class="small">{{ $selectedEnseignant->email }}</div>
                                                </div>
                                                <button type="button" class="btn btn-sm btn-alt-danger" 
                                                    wire:click="$set('ecForm.user_id', '')">
                                                    <i class="fa fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    @endif
                                @endif
                                
                                <!-- Recherche d'enseignant -->
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Rechercher un enseignant..." 
                                        wire:model.debounce.300ms="enseignantQuery">
                                    <button class="btn btn-alt-secondary" type="button">
                                        <i class="fa fa-search"></i>
                                    </button>
                                </div>
                                
                                <!-- Résultats de recherche -->
                                @if($filteredEnseignants && count($filteredEnseignants) > 0)
                                    <div class="list-group mt-2 mb-3">
                                        @foreach($filteredEnseignants as $enseignant)
                                            <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" 
                                                wire:click="selectEnseignant({{ $enseignant->id }})">
                                                <div>
                                                    <span class="fw-bold">{{ $enseignant->nom }} {{ $enseignant->prenom }}</span>
                                                    <small class="d-block text-muted">{{ $enseignant->email }}</small>
                                                </div>
                                                @if(isset($ecForm['user_id']) && $enseignant->id == $ecForm['user_id'])
                                                    <span class="badge bg-primary rounded-pill"><i class="fa fa-check"></i></span>
                                                @endif
                                            </button>
                                        @endforeach
                                    </div>
                                @elseif($enseignantQuery && strlen($enseignantQuery) >= 2)
                                    <div class="text-center text-muted my-3">
                                        <p>Aucun enseignant trouvé</p>
                                    </div>
                                @endif
                                
                                <!-- Enseignants récents -->
                                @if(count($recentlyAssignedEnseignants) > 0)
                                    <div class="mb-3">
                                        <label class="form-label">Enseignants récents</label>
                                        <div class="row g-2">
                                            @foreach($recentlyAssignedEnseignants as $ensId)
                                                @php
                                                    $e = App\Models\User::find($ensId);
                                                @endphp
                                                @if($e)
                                                    <div class="col-6">
                                                        <button type="button" 
                                                            class="btn btn-sm btn-block w-100 text-start @if(isset($ecForm['user_id']) && $ensId == $ecForm['user_id']) btn-primary @else btn-alt-secondary @endif"
                                                            wire:click="selectEnseignant({{ $ensId }})">
                                                            <div class="text-truncate">{{ $e->nom }} {{ $e->prenom }}</div>
                                                        </button>
                                                    </div>
                                                @endif
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                                
                                <!-- Liste déroulante complète -->
                                <div class="mb-3">
                                    <label class="form-label">Ou sélectionner dans la liste</label>
                                    <select class="form-select" wire:model="ecForm.user_id">
                                        <option value="">-- Sélectionner un enseignant --</option>
                                        @foreach ($enseignants as $enseignant)
                                            <option value="{{ $enseignant->id }}">
                                                {{ $enseignant->nom }} {{ $enseignant->prenom }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Formulaire d'ajout d'un nouvel enseignant -->
                    @if($showAddEnseignantForm)
                        <div class="alert alert-info">
                            <h5 class="mb-3">Ajouter un nouvel enseignant</h5>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Nom</label>
                                    <input type="text" class="form-control" wire:model.defer="newEnseignant.nom">
                                    @error('newEnseignant.nom') <span class="text-danger">{{ $message }}</span> @enderror
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Prénom</label>
                                    <input type="text" class="form-control" wire:model.defer="newEnseignant.prenom">
                                    @error('newEnseignant.prenom') <span class="text-danger">{{ $message }}</span> @enderror
                                </div>
                            </div>
                            
                            
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Téléphone</label>
                                    <input type="text" class="form-control" wire:model.defer="newEnseignant.telephone1">
                                    @error('newEnseignant.telephone1') <span class="text-danger">{{ $message }}</span> @enderror
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Sexe</label>
                                    <select class="form-select" wire:model.defer="newEnseignant.sexe">
                                        <option value="M">Homme</option>
                                        <option value="F">Femme</option>
                                    </select>
                                    @error('newEnseignant.sexe') <span class="text-danger">{{ $message }}</span> @enderror
                                </div>
                            </div>
                            
                            <div class="text-end">
                                <button type="button" class="btn btn-alt-secondary" wire:click="toggleAddEnseignantForm">
                                    Annuler
                                </button>
                                <button type="button" class="btn btn-alt-success" wire:click="createEnseignant">
                                    <i class="fa fa-check me-1"></i> Créer enseignant
                                </button>
                            </div>
                        </div>
                    @endif
                </div>
                
                <div class="block-content block-content-full block-content-sm text-end border-top">
                    <button type="button" class="btn btn-alt-secondary" wire:click="closeEcModal">
                        Annuler
                    </button>
                    <button type="button" class="btn btn-primary" wire:click="saveEC">
                        <i class="fa fa-check opacity-50 me-1"></i> 
                        {{ $ecModalMode === 'add' ? 'Ajouter' : 'Mettre à jour' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>
@endif

<!-- Include Duplication Modal -->
@include('livewire.deraq.ue.duplication-modal')

<script>
    // Select2 initialization for duplication modal
    document.addEventListener('livewire:load', function () {
        initDuplicationSelect2();

        // Reinitialize Select2 when Livewire refreshes the DOM
        Livewire.hook('message.processed', (message, component) => {
            setTimeout(() => {
                initDuplicationSelect2();
            }, 50);
        });

        // Close modal after successful duplication
        window.addEventListener('closeDuplicationModalAfterDelay', event => {
            setTimeout(() => {
                @this.call('closeDuplicationModal');
            }, 2000);
        });
    });

    function initDuplicationSelect2() {
        // Parcours multi-select for duplication
        if (document.getElementById('duplication-parcours-select')) {
            $('#duplication-parcours-select').select2({
                placeholder: 'Sélectionnez les parcours de destination',
                allowClear: true,
                closeOnSelect: false, // Keep dropdown open after selection
                width: '100%',
                dropdownParent: $('.modal-content'), // Ensure dropdown appears within modal
                dropdownCssClass: 'select2-dropdown-fixed'
            }).on('change', function (e) {
                // Don't update Livewire immediately - wait for blur event
            }).on('blur', function (e) {
                // Sync with Livewire when focus leaves the select element
                @this.set('targetParcours', $(this).val() || []);
            }).on('select2:close', function (e) {
                // Alternative approach - update when dropdown closes
                setTimeout(() => {
                    @this.set('targetParcours', $(this).val() || []);
                }, 100);
            });

            // Set initial values if any
            if (@this.targetParcours && @this.targetParcours.length > 0) {
                $('#duplication-parcours-select').val(@this.targetParcours).trigger('change');
            }
        }
    }

    // Prevent Livewire from updating while Select2 dropdown is open
    window.addEventListener('livewire:before-dom-update', () => {
        if ($('.select2-dropdown').is(':visible')) {
            $('.select2-container').select2('close');
        }
    });
</script>

<style>
    /* Custom styles for Select2 in modal */
    .select2-dropdown-fixed {
        z-index: 9999 !important;
    }

    .select2-container--default .select2-selection--multiple {
        min-height: 38px;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        background-color: #3498db;
        border: 1px solid #2980b9;
        color: white;
        border-radius: 0.25rem;
        padding: 2px 8px;
        margin: 2px;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
        color: white;
        margin-right: 5px;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
        color: #e74c3c;
    }
</style>
