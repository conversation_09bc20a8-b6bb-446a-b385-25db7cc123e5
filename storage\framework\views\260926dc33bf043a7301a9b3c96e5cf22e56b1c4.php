<?php $__env->startSection('js'); ?>
    <!-- jQuery (required for DataTables plugin) -->
    <script src="<?php echo e(asset('js/lib/jquery.min.js')); ?>"></script>

<?php $__env->stopSection(); ?>

<div wire:ignore.self>

    <?php if($currentPage == PAGECREATEFORM): ?>
        <?php echo $__env->make('livewire.deraq.mention.create', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <?php if($currentPage == PAGEEDITFORM): ?>
        <?php echo $__env->make('livewire.deraq.mention.edit', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <?php if($currentPage == PAGELIST): ?>
        <?php echo $__env->make('livewire.deraq.mention.liste', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

</div>

<script>
    window.addEventListener("showSuccessMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'success',
            icon: 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!'
        });
    })
</script>



<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/deraq/mention/index.blade.php ENDPATH**/ ?>