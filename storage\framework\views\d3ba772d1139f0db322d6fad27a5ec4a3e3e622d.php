 <!-- Hero -->
 <div class="bg-body-light">
     <div class="content content-full">

         <h1 class="h3 fw-bold mb-2">
             Gestion des Mentions
         </h1>

     </div>
 </div>
 <!-- END Hero -->

 <!-- Page Content -->
 <div class="content">

     <!-- Dynamic Table Full -->
     <div class="block block-rounded">
         <div class="block-header block-header-default">
             <h3 class="block-title">
                 Liste des Mentions
             </h3>
             <div class="block-options">
                 <a class="btn btn-sm btn-primary me-1" wire:click.prevent="goToAddMention()">
                     <i class="fa fa-plus me-1"></i> Nouveau Mention
                 </a>
             </div>
         </div>
         <div class="block-content block-content-full">
             <div class="row">
                 <div class="col-sm-12 col-md-6">
                     <div class="d-flex">
                         <div>
                             <label>
                                 <select wire:model="filtreDomaine" class="form-select form-select-sm">
                                     <option selected value="">Filtre Mention</option>
                                     <?php $__currentLoopData = $domaines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $domaine): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                         <option value="<?php echo e($domaine->id); ?>"><?php echo e($domaine->nom); ?></option>
                                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                 </select>
                             </label>
                         </div>
                         <div>
                             <label>
                                 <select wire:model="filtreNiveau" class="form-select form-select-sm">
                                     <option selected value="">Filtre Niveau</option>
                                     <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                         <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                 </select>
                             </label>
                         </div>
                         
                     </div>

                 </div>
                 <div class="col-sm-12 col-md-6 text-end">

                     <label>
                         <input type="search" wire:model="query" class="form-control form-control-sm"
                             placeholder="Search..">
                     </label>

                 </div>
             </div>
             <!-- DataTables init on table by adding .js-dataTable-full class, functionality is initialized in js/pages/tables_datatables.js -->
             <table class="table table-bordered table-striped table-vcenter">
                 <thead>
                     <tr>
                         <th class="text-center">#</th>
                         <th>Nom</th>
                         <th class="d-none d-sm-table-cell">Domaine</th>
                         <th class="text-center" style="width: 120px;">Actions</th>
                     </tr>
                 </thead>
                 <tbody>

                     <?php $__currentLoopData = $mentions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mention): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                         <tr>
                             <td class="text-center fw-semibold">
                                 <?php echo e($mention->id); ?>

                             </td>
                             <td class="fw-semibold">
                                 <?php echo e($mention->nom); ?>

                             </td>
                             <td class="fw-semibold d-none d-sm-table-cell">
                                 <?php echo e($mention->domaine->nom); ?>

                             </td>
                             <td class="text-center">
                                 <div class="btn-group mb-2">
                                     <button type="button" wire:click="goToEditMention(<?php echo e($mention->id); ?>)"
                                         class="btn btn-sm btn-alt-secondary" title="Edit">
                                         <i class="fa fa-fw fa-pencil-alt"></i>
                                     </button>
                                     <button type="button" wire:click="deleteMention(<?php echo e($mention->id); ?>)"
                                         class="btn btn-sm btn-alt-secondary" title="Delete">
                                         <i class="fa fa-fw fa-times"></i>
                                     </button>
                                 </div>
                                 <a href="<?php echo e(route('deraq.pedagogiques.parcours.add', ['mentionId' => $mention->id])); ?>"
                                     class="btn btn-sm btn-alt-secondary">
                                     <i class="fa fa-plus"></i>
                                     Parcours
                                 </a>
                             </td>
                         </tr>
                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



                 </tbody>
             </table>

             <nav aria-label="Photos Search Navigation">
                 <ul class="pagination pagination-sm justify-content-end mt-2">
                     <?php echo e($mentions->links()); ?>

                 </ul>
             </nav>
         </div>
     </div>
     <!-- END Dynamic Table Full -->


 </div>
 <!-- END Page Content -->
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/deraq/mention/liste.blade.php ENDPATH**/ ?>