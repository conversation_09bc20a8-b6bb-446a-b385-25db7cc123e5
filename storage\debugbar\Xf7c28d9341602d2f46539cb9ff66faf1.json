{"__meta": {"id": "Xf7c28d9341602d2f46539cb9ff66faf1", "datetime": "2025-09-17 10:08:53", "utime": **********.651526, "method": "POST", "uri": "/livewire/message/ues", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 6, "messages": [{"message": "[10:08:53] LOG.info: Detected N+1 Query", "message_html": null, "is_string": false, "label": "info", "time": **********.649198, "collector": "log"}, {"message": "[10:08:53] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Parcour\r\nNum-Called: 3\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Ues.php:567\r\n#18 \\storage\\framework\\views\\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100\r\n#20 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#21 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#25 \\storage\\framework\\views\\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667\r\n#27 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#32 \\storage\\framework\\views\\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18\r\n#34 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#35 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#39 \\vendor\\livewire\\livewire\\src\\Component.php:235\r\n#40 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php:14\r\n#41 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:154\r\n#42 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:15\r\n#43 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.649437, "collector": "log"}, {"message": "[10:08:53] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Niveau\r\nNum-Called: 3\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Ues.php:567\r\n#18 \\storage\\framework\\views\\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100\r\n#20 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#21 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#25 \\storage\\framework\\views\\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667\r\n#27 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#32 \\storage\\framework\\views\\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18\r\n#34 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#35 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#39 \\vendor\\livewire\\livewire\\src\\Component.php:235\r\n#40 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php:14\r\n#41 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:154\r\n#42 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:15\r\n#43 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.649637, "collector": "log"}, {"message": "[10:08:53] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Semestre\r\nNum-Called: 3\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Ues.php:567\r\n#18 \\storage\\framework\\views\\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100\r\n#20 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#21 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#25 \\storage\\framework\\views\\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667\r\n#27 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#32 \\storage\\framework\\views\\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18\r\n#34 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#35 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#39 \\vendor\\livewire\\livewire\\src\\Component.php:235\r\n#40 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php:14\r\n#41 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:154\r\n#42 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:15\r\n#43 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.649763, "collector": "log"}, {"message": "[10:08:53] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\AnneeUniversitaire\r\nNum-Called: 3\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Ues.php:567\r\n#18 \\storage\\framework\\views\\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100\r\n#20 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#21 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#25 \\storage\\framework\\views\\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667\r\n#27 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#32 \\storage\\framework\\views\\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18\r\n#34 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#35 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#39 \\vendor\\livewire\\livewire\\src\\Component.php:235\r\n#40 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php:14\r\n#41 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:154\r\n#42 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:15\r\n#43 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.649879, "collector": "log"}, {"message": "[10:08:53] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Matiere\r\nNum-Called: 3\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Ues.php:567\r\n#18 \\storage\\framework\\views\\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100\r\n#20 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#21 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#25 \\storage\\framework\\views\\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667\r\n#27 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#32 \\storage\\framework\\views\\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18\r\n#34 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#35 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#39 \\vendor\\livewire\\livewire\\src\\Component.php:235\r\n#40 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php:14\r\n#41 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:154\r\n#42 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:15\r\n#43 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.649987, "collector": "log"}]}, "time": {"start": **********.738466, "end": **********.651558, "duration": 0.****************, "duration_str": "913ms", "measures": [{"label": "Booting", "start": **********.738466, "relative_start": 0, "end": **********.065785, "relative_end": **********.065785, "duration": 0.****************, "duration_str": "327ms", "params": [], "collector": null}, {"label": "Application", "start": **********.066332, "relative_start": 0.****************, "end": **********.65156, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "585ms", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "livewire.deraq.ue.index (\\resources\\views\\livewire\\deraq\\ue\\index.blade.php)", "param_count": 44, "params": ["ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "errors", "_instance", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/index.blade.php&line=0"}, {"name": "livewire.deraq.ue.liste (\\resources\\views\\livewire\\deraq\\ue\\liste.blade.php)", "param_count": 46, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}, {"name": "livewire.deraq.ue.duplication-modal (\\resources\\views\\livewire\\deraq\\ue\\duplication-modal.blade.php)", "param_count": 52, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators", "__currentLoopData", "parcour", "loop", "niveau", "annee", "ue"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/duplication-modal.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 32, "nb_failed_statements": 0, "accumulated_duration": 0.05929000000000001, "accumulated_duration_str": "59.29ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00467, "duration_str": "4.67ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 7.877}, {"sql": "select * from `ues` where `annee_universitaire_id` = '5' and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 515}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00299, "duration_str": "2.99ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 7.877, "width_percent": 5.043}, {"sql": "select * from `parcours` where `parcours`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 10, 15, 24) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 515}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 12.92, "width_percent": 1.552}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1, 2, 3) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 515}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 14.471, "width_percent": 1.72}, {"sql": "select * from `semestres` where `semestres`.`id` in (1, 2, 3, 4, 5, 6, 8) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 515}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 16.192, "width_percent": 2.311}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (5) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 515}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0010400000000000001, "duration_str": "1.04ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 18.502, "width_percent": 1.754}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 262, 263, 264, 265, 276, 277, 278, 280, 282, 283, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 353, 354, 355, 356, 358, 359, 360, 361, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 515}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01326, "duration_str": "13.26ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 20.256, "width_percent": 22.365}, {"sql": "select * from `ues` where `annee_universitaire_id` = '5' and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 526}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 118}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00165, "duration_str": "1.65ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 42.621, "width_percent": 2.783}, {"sql": "select * from `parcours` where `parcours`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 10, 15, 24) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 526}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 118}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 45.404, "width_percent": 1.383}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1, 2, 3) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 526}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 118}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00139, "duration_str": "1.39ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 46.787, "width_percent": 2.344}, {"sql": "select * from `semestres` where `semestres`.`id` in (1, 2, 3, 4, 5, 6, 8) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 526}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 118}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 49.131, "width_percent": 1.214}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (5) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 526}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 118}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 50.346, "width_percent": 1.535}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 262, 263, 264, 265, 276, 277, 278, 280, 282, 283, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 353, 354, 355, 356, 358, 359, 360, 361, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 526}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 118}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00362, "duration_str": "3.62ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 51.881, "width_percent": 6.106}, {"sql": "select count(*) as aggregate from `ues` where `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 57.986, "width_percent": 1.467}, {"sql": "select * from `ues` where `ues`.`deleted_at` is null limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 59.454, "width_percent": 1.265}, {"sql": "select * from `parcours` where `parcours`.`id` in (2, 4, 5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 60.719, "width_percent": 1.417}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00184, "duration_str": "1.84ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 62.135, "width_percent": 3.103}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 65.239, "width_percent": 1.113}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 66.352, "width_percent": 1.214}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (80, 84, 85, 87, 88, 89, 90, 91, 92, 93) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 67.566, "width_percent": 1.518}, {"sql": "select * from `users` where `users`.`id` in (145, 147, 148, 149, 151, 155, 156, 157, 158, 159, 177, 180) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00128, "duration_str": "1.28ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 69.084, "width_percent": 2.159}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 71.243, "width_percent": 1.231}, {"sql": "select * from `semestres` where `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 124}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00155, "duration_str": "1.55ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:124", "connection": "imsaaapp", "start_percent": 72.474, "width_percent": 2.614}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 125}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00098, "duration_str": "980μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:125", "connection": "imsaaapp", "start_percent": 75.089, "width_percent": 1.653}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 126}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:126", "connection": "imsaaapp", "start_percent": 76.741, "width_percent": 1.147}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 2) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 127}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00214, "duration_str": "2.14ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:127", "connection": "imsaaapp", "start_percent": 77.888, "width_percent": 3.609}, {"sql": "select * from `ues` where `annee_universitaire_id` = '5' and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 15, "namespace": "view", "name": "0b5e646256704a78cdceca5c9decbf8c72b1cd7e", "line": 100}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "duration": 0.0016699999999999998, "duration_str": "1.67ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 81.498, "width_percent": 2.817}, {"sql": "select * from `parcours` where `parcours`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 10, 15, 24) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": "view", "name": "0b5e646256704a78cdceca5c9decbf8c72b1cd7e", "line": 100}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 84.314, "width_percent": 1.265}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1, 2, 3) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": "view", "name": "0b5e646256704a78cdceca5c9decbf8c72b1cd7e", "line": 100}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 85.579, "width_percent": 1.13}, {"sql": "select * from `semestres` where `semestres`.`id` in (1, 2, 3, 4, 5, 6, 8) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": "view", "name": "0b5e646256704a78cdceca5c9decbf8c72b1cd7e", "line": 100}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "duration": 0.0011799999999999998, "duration_str": "1.18ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 86.709, "width_percent": 1.99}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (5) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": "view", "name": "0b5e646256704a78cdceca5c9decbf8c72b1cd7e", "line": 100}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 88.7, "width_percent": 1.45}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 262, 263, 264, 265, 276, 277, 278, 280, 282, 283, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 353, 354, 355, 356, 358, 359, 360, 361, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": "view", "name": "0b5e646256704a78cdceca5c9decbf8c72b1cd7e", "line": 100}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "duration": 0.00584, "duration_str": "5.84ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 90.15, "width_percent": 9.85}]}, "models": {"data": {"App\\Models\\Matiere": 1278, "App\\Models\\AnneeUniversitaire": 11, "App\\Models\\Semestre": 32, "App\\Models\\Niveau": 15, "App\\Models\\Parcour": 60, "App\\Models\\Ue": 631, "App\\Models\\User": 64}, "count": 2091}, "livewire": {"data": {"ues #wPVVDhRblUJmtSrRYom7": "array:5 [\n  \"data\" => array:34 [\n    \"currentPage\" => \"liste\"\n    \"query\" => \"\"\n    \"selectedParcours\" => []\n    \"selectedNiveaux\" => []\n    \"filtreAnnee\" => \"\"\n    \"newUe\" => []\n    \"editUe\" => []\n    \"editingUeId\" => null\n    \"expandedUes\" => []\n    \"showQuickAddModal\" => false\n    \"showEcModal\" => false\n    \"ecModalMode\" => \"add\"\n    \"ecForm\" => []\n    \"currentEcUe\" => null\n    \"currentEcId\" => null\n    \"showDuplicationModal\" => true\n    \"duplicationStep\" => \"selection\"\n    \"selectedUesForDuplication\" => []\n    \"targetParcours\" => []\n    \"targetAnneeId\" => \"\"\n    \"duplicationPreview\" => []\n    \"editablePreviewData\" => []\n    \"duplicationResults\" => []\n    \"duplicationFilterAnnee\" => \"5\"\n    \"duplicationFilterParcours\" => []\n    \"duplicationQuery\" => \"\"\n    \"selectAllUes\" => false\n    \"enseignantQuery\" => \"\"\n    \"filteredEnseignants\" => []\n    \"showAddEnseignantForm\" => false\n    \"newEnseignant\" => array:5 [\n      \"nom\" => \"\"\n      \"prenom\" => \"\"\n      \"email\" => \"\"\n      \"telephone1\" => \"\"\n      \"sexe\" => \"M\"\n    ]\n    \"recentlyAssignedEnseignants\" => []\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"ues\"\n  \"view\" => \"livewire.deraq.ue.index\"\n  \"component\" => \"App\\Http\\Livewire\\Ues\"\n  \"id\" => \"wPVVDhRblUJmtSrRYom7\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pedagogiques/ue\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1758092143\n]"}, "request": {"path_info": "/livewire/message/ues", "status_code": "<pre class=sf-dump id=sf-dump-315121697 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-315121697\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1495032134 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1495032134\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1502917335 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">wPVVDhRblUJmtSrRYom7</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ues</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">pedagogiques/ue</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">d9ddd3ba</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:34</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>selectedParcours</span>\" => []\n      \"<span class=sf-dump-key>selectedNiveaux</span>\" => []\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => \"\"\n      \"<span class=sf-dump-key>newUe</span>\" => []\n      \"<span class=sf-dump-key>editUe</span>\" => []\n      \"<span class=sf-dump-key>editingUeId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>expandedUes</span>\" => []\n      \"<span class=sf-dump-key>showQuickAddModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEcModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>ecModalMode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">add</span>\"\n      \"<span class=sf-dump-key>ecForm</span>\" => []\n      \"<span class=sf-dump-key>currentEcUe</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentEcId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>showDuplicationModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>duplicationStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">selection</span>\"\n      \"<span class=sf-dump-key>selectedUesForDuplication</span>\" => <span class=sf-dump-note>array:207</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>0</span> => <span class=sf-dump-num>234</span>\n        <span class=sf-dump-key>2</span> => <span class=sf-dump-num>235</span>\n        <span class=sf-dump-key>3</span> => <span class=sf-dump-num>236</span>\n        <span class=sf-dump-key>4</span> => <span class=sf-dump-num>237</span>\n        <span class=sf-dump-key>5</span> => <span class=sf-dump-num>238</span>\n        <span class=sf-dump-key>6</span> => <span class=sf-dump-num>239</span>\n        <span class=sf-dump-key>7</span> => <span class=sf-dump-num>240</span>\n        <span class=sf-dump-key>8</span> => <span class=sf-dump-num>241</span>\n        <span class=sf-dump-key>9</span> => <span class=sf-dump-num>242</span>\n        <span class=sf-dump-key>10</span> => <span class=sf-dump-num>243</span>\n        <span class=sf-dump-key>11</span> => <span class=sf-dump-num>244</span>\n        <span class=sf-dump-key>12</span> => <span class=sf-dump-num>245</span>\n        <span class=sf-dump-key>13</span> => <span class=sf-dump-num>246</span>\n        <span class=sf-dump-key>14</span> => <span class=sf-dump-num>247</span>\n        <span class=sf-dump-key>15</span> => <span class=sf-dump-num>248</span>\n        <span class=sf-dump-key>16</span> => <span class=sf-dump-num>249</span>\n        <span class=sf-dump-key>17</span> => <span class=sf-dump-num>250</span>\n        <span class=sf-dump-key>18</span> => <span class=sf-dump-num>251</span>\n        <span class=sf-dump-key>19</span> => <span class=sf-dump-num>252</span>\n        <span class=sf-dump-key>20</span> => <span class=sf-dump-num>253</span>\n        <span class=sf-dump-key>21</span> => <span class=sf-dump-num>254</span>\n        <span class=sf-dump-key>22</span> => <span class=sf-dump-num>255</span>\n        <span class=sf-dump-key>23</span> => <span class=sf-dump-num>256</span>\n        <span class=sf-dump-key>24</span> => <span class=sf-dump-num>257</span>\n        <span class=sf-dump-key>25</span> => <span class=sf-dump-num>258</span>\n        <span class=sf-dump-key>26</span> => <span class=sf-dump-num>259</span>\n        <span class=sf-dump-key>27</span> => <span class=sf-dump-num>262</span>\n        <span class=sf-dump-key>28</span> => <span class=sf-dump-num>263</span>\n        <span class=sf-dump-key>29</span> => <span class=sf-dump-num>264</span>\n        <span class=sf-dump-key>30</span> => <span class=sf-dump-num>265</span>\n        <span class=sf-dump-key>31</span> => <span class=sf-dump-num>276</span>\n        <span class=sf-dump-key>32</span> => <span class=sf-dump-num>277</span>\n        <span class=sf-dump-key>33</span> => <span class=sf-dump-num>278</span>\n        <span class=sf-dump-key>34</span> => <span class=sf-dump-num>280</span>\n        <span class=sf-dump-key>35</span> => <span class=sf-dump-num>282</span>\n        <span class=sf-dump-key>36</span> => <span class=sf-dump-num>283</span>\n        <span class=sf-dump-key>37</span> => <span class=sf-dump-num>286</span>\n        <span class=sf-dump-key>38</span> => <span class=sf-dump-num>287</span>\n        <span class=sf-dump-key>39</span> => <span class=sf-dump-num>288</span>\n        <span class=sf-dump-key>40</span> => <span class=sf-dump-num>289</span>\n        <span class=sf-dump-key>41</span> => <span class=sf-dump-num>290</span>\n        <span class=sf-dump-key>42</span> => <span class=sf-dump-num>291</span>\n        <span class=sf-dump-key>43</span> => <span class=sf-dump-num>292</span>\n        <span class=sf-dump-key>44</span> => <span class=sf-dump-num>293</span>\n        <span class=sf-dump-key>45</span> => <span class=sf-dump-num>294</span>\n        <span class=sf-dump-key>46</span> => <span class=sf-dump-num>295</span>\n        <span class=sf-dump-key>47</span> => <span class=sf-dump-num>296</span>\n        <span class=sf-dump-key>48</span> => <span class=sf-dump-num>297</span>\n        <span class=sf-dump-key>49</span> => <span class=sf-dump-num>298</span>\n        <span class=sf-dump-key>50</span> => <span class=sf-dump-num>299</span>\n        <span class=sf-dump-key>51</span> => <span class=sf-dump-num>300</span>\n        <span class=sf-dump-key>52</span> => <span class=sf-dump-num>301</span>\n        <span class=sf-dump-key>53</span> => <span class=sf-dump-num>302</span>\n        <span class=sf-dump-key>54</span> => <span class=sf-dump-num>303</span>\n        <span class=sf-dump-key>55</span> => <span class=sf-dump-num>304</span>\n        <span class=sf-dump-key>56</span> => <span class=sf-dump-num>305</span>\n        <span class=sf-dump-key>57</span> => <span class=sf-dump-num>306</span>\n        <span class=sf-dump-key>58</span> => <span class=sf-dump-num>307</span>\n        <span class=sf-dump-key>59</span> => <span class=sf-dump-num>308</span>\n        <span class=sf-dump-key>60</span> => <span class=sf-dump-num>309</span>\n        <span class=sf-dump-key>61</span> => <span class=sf-dump-num>310</span>\n        <span class=sf-dump-key>62</span> => <span class=sf-dump-num>311</span>\n        <span class=sf-dump-key>63</span> => <span class=sf-dump-num>312</span>\n        <span class=sf-dump-key>64</span> => <span class=sf-dump-num>313</span>\n        <span class=sf-dump-key>65</span> => <span class=sf-dump-num>314</span>\n        <span class=sf-dump-key>66</span> => <span class=sf-dump-num>315</span>\n        <span class=sf-dump-key>67</span> => <span class=sf-dump-num>316</span>\n        <span class=sf-dump-key>68</span> => <span class=sf-dump-num>317</span>\n        <span class=sf-dump-key>69</span> => <span class=sf-dump-num>318</span>\n        <span class=sf-dump-key>70</span> => <span class=sf-dump-num>319</span>\n        <span class=sf-dump-key>71</span> => <span class=sf-dump-num>320</span>\n        <span class=sf-dump-key>72</span> => <span class=sf-dump-num>321</span>\n        <span class=sf-dump-key>73</span> => <span class=sf-dump-num>322</span>\n        <span class=sf-dump-key>74</span> => <span class=sf-dump-num>323</span>\n        <span class=sf-dump-key>75</span> => <span class=sf-dump-num>324</span>\n        <span class=sf-dump-key>76</span> => <span class=sf-dump-num>325</span>\n        <span class=sf-dump-key>77</span> => <span class=sf-dump-num>326</span>\n        <span class=sf-dump-key>78</span> => <span class=sf-dump-num>327</span>\n        <span class=sf-dump-key>79</span> => <span class=sf-dump-num>328</span>\n        <span class=sf-dump-key>80</span> => <span class=sf-dump-num>329</span>\n        <span class=sf-dump-key>81</span> => <span class=sf-dump-num>330</span>\n        <span class=sf-dump-key>82</span> => <span class=sf-dump-num>331</span>\n        <span class=sf-dump-key>83</span> => <span class=sf-dump-num>332</span>\n        <span class=sf-dump-key>84</span> => <span class=sf-dump-num>333</span>\n        <span class=sf-dump-key>85</span> => <span class=sf-dump-num>334</span>\n        <span class=sf-dump-key>86</span> => <span class=sf-dump-num>335</span>\n        <span class=sf-dump-key>87</span> => <span class=sf-dump-num>336</span>\n        <span class=sf-dump-key>88</span> => <span class=sf-dump-num>337</span>\n        <span class=sf-dump-key>89</span> => <span class=sf-dump-num>338</span>\n        <span class=sf-dump-key>90</span> => <span class=sf-dump-num>339</span>\n        <span class=sf-dump-key>91</span> => <span class=sf-dump-num>340</span>\n        <span class=sf-dump-key>92</span> => <span class=sf-dump-num>341</span>\n        <span class=sf-dump-key>93</span> => <span class=sf-dump-num>342</span>\n        <span class=sf-dump-key>94</span> => <span class=sf-dump-num>343</span>\n        <span class=sf-dump-key>95</span> => <span class=sf-dump-num>344</span>\n        <span class=sf-dump-key>96</span> => <span class=sf-dump-num>345</span>\n        <span class=sf-dump-key>97</span> => <span class=sf-dump-num>346</span>\n        <span class=sf-dump-key>98</span> => <span class=sf-dump-num>347</span>\n        <span class=sf-dump-key>99</span> => <span class=sf-dump-num>348</span>\n        <span class=sf-dump-key>100</span> => <span class=sf-dump-num>349</span>\n        <span class=sf-dump-key>101</span> => <span class=sf-dump-num>350</span>\n        <span class=sf-dump-key>102</span> => <span class=sf-dump-num>351</span>\n        <span class=sf-dump-key>103</span> => <span class=sf-dump-num>353</span>\n        <span class=sf-dump-key>104</span> => <span class=sf-dump-num>354</span>\n        <span class=sf-dump-key>105</span> => <span class=sf-dump-num>355</span>\n        <span class=sf-dump-key>106</span> => <span class=sf-dump-num>356</span>\n        <span class=sf-dump-key>107</span> => <span class=sf-dump-num>358</span>\n        <span class=sf-dump-key>108</span> => <span class=sf-dump-num>359</span>\n        <span class=sf-dump-key>109</span> => <span class=sf-dump-num>360</span>\n        <span class=sf-dump-key>110</span> => <span class=sf-dump-num>361</span>\n        <span class=sf-dump-key>111</span> => <span class=sf-dump-num>370</span>\n        <span class=sf-dump-key>112</span> => <span class=sf-dump-num>371</span>\n        <span class=sf-dump-key>113</span> => <span class=sf-dump-num>372</span>\n        <span class=sf-dump-key>114</span> => <span class=sf-dump-num>373</span>\n        <span class=sf-dump-key>115</span> => <span class=sf-dump-num>374</span>\n        <span class=sf-dump-key>116</span> => <span class=sf-dump-num>375</span>\n        <span class=sf-dump-key>117</span> => <span class=sf-dump-num>376</span>\n        <span class=sf-dump-key>118</span> => <span class=sf-dump-num>377</span>\n        <span class=sf-dump-key>119</span> => <span class=sf-dump-num>378</span>\n        <span class=sf-dump-key>120</span> => <span class=sf-dump-num>379</span>\n        <span class=sf-dump-key>121</span> => <span class=sf-dump-num>380</span>\n        <span class=sf-dump-key>122</span> => <span class=sf-dump-num>381</span>\n        <span class=sf-dump-key>123</span> => <span class=sf-dump-num>382</span>\n        <span class=sf-dump-key>124</span> => <span class=sf-dump-num>383</span>\n        <span class=sf-dump-key>125</span> => <span class=sf-dump-num>384</span>\n        <span class=sf-dump-key>126</span> => <span class=sf-dump-num>385</span>\n        <span class=sf-dump-key>127</span> => <span class=sf-dump-num>386</span>\n        <span class=sf-dump-key>128</span> => <span class=sf-dump-num>387</span>\n        <span class=sf-dump-key>129</span> => <span class=sf-dump-num>388</span>\n        <span class=sf-dump-key>130</span> => <span class=sf-dump-num>389</span>\n        <span class=sf-dump-key>131</span> => <span class=sf-dump-num>390</span>\n        <span class=sf-dump-key>132</span> => <span class=sf-dump-num>391</span>\n        <span class=sf-dump-key>133</span> => <span class=sf-dump-num>392</span>\n        <span class=sf-dump-key>134</span> => <span class=sf-dump-num>393</span>\n        <span class=sf-dump-key>135</span> => <span class=sf-dump-num>394</span>\n        <span class=sf-dump-key>136</span> => <span class=sf-dump-num>395</span>\n        <span class=sf-dump-key>137</span> => <span class=sf-dump-num>396</span>\n        <span class=sf-dump-key>138</span> => <span class=sf-dump-num>397</span>\n        <span class=sf-dump-key>139</span> => <span class=sf-dump-num>398</span>\n        <span class=sf-dump-key>140</span> => <span class=sf-dump-num>399</span>\n        <span class=sf-dump-key>141</span> => <span class=sf-dump-num>400</span>\n        <span class=sf-dump-key>142</span> => <span class=sf-dump-num>401</span>\n        <span class=sf-dump-key>143</span> => <span class=sf-dump-num>402</span>\n        <span class=sf-dump-key>144</span> => <span class=sf-dump-num>403</span>\n        <span class=sf-dump-key>145</span> => <span class=sf-dump-num>404</span>\n        <span class=sf-dump-key>146</span> => <span class=sf-dump-num>405</span>\n        <span class=sf-dump-key>147</span> => <span class=sf-dump-num>406</span>\n        <span class=sf-dump-key>148</span> => <span class=sf-dump-num>407</span>\n        <span class=sf-dump-key>149</span> => <span class=sf-dump-num>408</span>\n        <span class=sf-dump-key>150</span> => <span class=sf-dump-num>409</span>\n        <span class=sf-dump-key>151</span> => <span class=sf-dump-num>410</span>\n        <span class=sf-dump-key>152</span> => <span class=sf-dump-num>411</span>\n        <span class=sf-dump-key>153</span> => <span class=sf-dump-num>412</span>\n        <span class=sf-dump-key>154</span> => <span class=sf-dump-num>413</span>\n        <span class=sf-dump-key>155</span> => <span class=sf-dump-num>414</span>\n        <span class=sf-dump-key>156</span> => <span class=sf-dump-num>415</span>\n        <span class=sf-dump-key>157</span> => <span class=sf-dump-num>416</span>\n        <span class=sf-dump-key>158</span> => <span class=sf-dump-num>417</span>\n        <span class=sf-dump-key>159</span> => <span class=sf-dump-num>418</span>\n        <span class=sf-dump-key>160</span> => <span class=sf-dump-num>419</span>\n        <span class=sf-dump-key>161</span> => <span class=sf-dump-num>420</span>\n        <span class=sf-dump-key>162</span> => <span class=sf-dump-num>421</span>\n        <span class=sf-dump-key>163</span> => <span class=sf-dump-num>422</span>\n        <span class=sf-dump-key>164</span> => <span class=sf-dump-num>423</span>\n        <span class=sf-dump-key>165</span> => <span class=sf-dump-num>424</span>\n        <span class=sf-dump-key>166</span> => <span class=sf-dump-num>426</span>\n        <span class=sf-dump-key>167</span> => <span class=sf-dump-num>427</span>\n        <span class=sf-dump-key>168</span> => <span class=sf-dump-num>428</span>\n        <span class=sf-dump-key>169</span> => <span class=sf-dump-num>429</span>\n        <span class=sf-dump-key>170</span> => <span class=sf-dump-num>430</span>\n        <span class=sf-dump-key>171</span> => <span class=sf-dump-num>431</span>\n        <span class=sf-dump-key>172</span> => <span class=sf-dump-num>432</span>\n        <span class=sf-dump-key>173</span> => <span class=sf-dump-num>433</span>\n        <span class=sf-dump-key>174</span> => <span class=sf-dump-num>434</span>\n        <span class=sf-dump-key>175</span> => <span class=sf-dump-num>435</span>\n        <span class=sf-dump-key>176</span> => <span class=sf-dump-num>436</span>\n        <span class=sf-dump-key>177</span> => <span class=sf-dump-num>437</span>\n        <span class=sf-dump-key>178</span> => <span class=sf-dump-num>438</span>\n        <span class=sf-dump-key>179</span> => <span class=sf-dump-num>439</span>\n        <span class=sf-dump-key>180</span> => <span class=sf-dump-num>440</span>\n        <span class=sf-dump-key>181</span> => <span class=sf-dump-num>441</span>\n        <span class=sf-dump-key>182</span> => <span class=sf-dump-num>442</span>\n        <span class=sf-dump-key>183</span> => <span class=sf-dump-num>443</span>\n        <span class=sf-dump-key>184</span> => <span class=sf-dump-num>444</span>\n        <span class=sf-dump-key>185</span> => <span class=sf-dump-num>445</span>\n        <span class=sf-dump-key>186</span> => <span class=sf-dump-num>446</span>\n        <span class=sf-dump-key>187</span> => <span class=sf-dump-num>447</span>\n        <span class=sf-dump-key>188</span> => <span class=sf-dump-num>448</span>\n        <span class=sf-dump-key>189</span> => <span class=sf-dump-num>449</span>\n        <span class=sf-dump-key>190</span> => <span class=sf-dump-num>450</span>\n        <span class=sf-dump-key>191</span> => <span class=sf-dump-num>451</span>\n        <span class=sf-dump-key>192</span> => <span class=sf-dump-num>452</span>\n        <span class=sf-dump-key>193</span> => <span class=sf-dump-num>453</span>\n        <span class=sf-dump-key>194</span> => <span class=sf-dump-num>454</span>\n        <span class=sf-dump-key>195</span> => <span class=sf-dump-num>455</span>\n        <span class=sf-dump-key>196</span> => <span class=sf-dump-num>456</span>\n        <span class=sf-dump-key>197</span> => <span class=sf-dump-num>457</span>\n        <span class=sf-dump-key>198</span> => <span class=sf-dump-num>458</span>\n        <span class=sf-dump-key>199</span> => <span class=sf-dump-num>459</span>\n        <span class=sf-dump-key>200</span> => <span class=sf-dump-num>460</span>\n        <span class=sf-dump-key>201</span> => <span class=sf-dump-num>461</span>\n        <span class=sf-dump-key>202</span> => <span class=sf-dump-num>462</span>\n        <span class=sf-dump-key>203</span> => <span class=sf-dump-num>463</span>\n        <span class=sf-dump-key>204</span> => <span class=sf-dump-num>464</span>\n        <span class=sf-dump-key>205</span> => <span class=sf-dump-num>465</span>\n        <span class=sf-dump-key>206</span> => <span class=sf-dump-num>466</span>\n        <span class=sf-dump-key>207</span> => <span class=sf-dump-num>467</span>\n      </samp>]\n      \"<span class=sf-dump-key>targetParcours</span>\" => []\n      \"<span class=sf-dump-key>targetAnneeId</span>\" => \"\"\n      \"<span class=sf-dump-key>duplicationPreview</span>\" => []\n      \"<span class=sf-dump-key>editablePreviewData</span>\" => []\n      \"<span class=sf-dump-key>duplicationResults</span>\" => []\n      \"<span class=sf-dump-key>duplicationFilterAnnee</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>duplicationFilterParcours</span>\" => []\n      \"<span class=sf-dump-key>duplicationQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>selectAllUes</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>enseignantQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>filteredEnseignants</span>\" => []\n      \"<span class=sf-dump-key>showAddEnseignantForm</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>newEnseignant</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>nom</span>\" => \"\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"\"\n        \"<span class=sf-dump-key>email</span>\" => \"\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"\"\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>M</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>recentlyAssignedEnseignants</span>\" => []\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">b3b2569a5b9751c2a5959abb7027c7125ef9023f5dcee9c6b62bcc7cd1b78526</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">cns</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"18 characters\">toggleSelectAllUes</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">syncInput</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">rr5s</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">selectAllUes</span>\"\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502917335\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1766634386 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3217</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik05NzcyckgyNGh2Z09SOW9XdkRrdmc9PSIsInZhbHVlIjoieWl4c3NRTFlJc0d1cDJNRVlkNGpNek83L2FjMWNGME1zT0llcW0wN2M2bmxWU2pzdHRubU1ZeGRxUVI5SDhCUVdNUkZjOGFPTDdKNVBPZmxVbE9LaUY4cGNNMHUwd01UK1hqSUlySm1sck1jWDhNbVBwZHB3WDFUTTYrSXhJTU0iLCJtYWMiOiJhYWI4ZThiN2QzZTZiN2JhMjgxN2E5NTJkM2FlZTYxMTEzNTY2N2ZjODZjYjRlOTJlZDkwMzg2M2FmNzIwOTgwIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlhYN1ZTMkorSVpwVk9ETXNhbDU2ekE9PSIsInZhbHVlIjoiSnlYSmJkQ2pmdDhIck9pcXB6NU9pNElBU05FTGVoSHg4N0puNi8vVWpWNDdXQ3Rtdkt5cXB5TnhkRzNleldTakt0WXh2WWU5cWtoT1NYVDQ1eXBIUkJlMnU1Q2lTdnVXdnpsT0hlN0tuQUVMU1Vwb21tZW1WbEV6clhTRWVoSDkiLCJtYWMiOiJiNGM2N2FiZTkwMzk1OThkMDc3MGEyOTc0NTFjNDk4Yjc0ZTFhNTU2NThmYjFiZjBlNzM4ZDExNTVmMGEwMzRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766634386\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1721218416 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56177</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3217</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3217</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik05NzcyckgyNGh2Z09SOW9XdkRrdmc9PSIsInZhbHVlIjoieWl4c3NRTFlJc0d1cDJNRVlkNGpNek83L2FjMWNGME1zT0llcW0wN2M2bmxWU2pzdHRubU1ZeGRxUVI5SDhCUVdNUkZjOGFPTDdKNVBPZmxVbE9LaUY4cGNNMHUwd01UK1hqSUlySm1sck1jWDhNbVBwZHB3WDFUTTYrSXhJTU0iLCJtYWMiOiJhYWI4ZThiN2QzZTZiN2JhMjgxN2E5NTJkM2FlZTYxMTEzNTY2N2ZjODZjYjRlOTJlZDkwMzg2M2FmNzIwOTgwIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlhYN1ZTMkorSVpwVk9ETXNhbDU2ekE9PSIsInZhbHVlIjoiSnlYSmJkQ2pmdDhIck9pcXB6NU9pNElBU05FTGVoSHg4N0puNi8vVWpWNDdXQ3Rtdkt5cXB5TnhkRzNleldTakt0WXh2WWU5cWtoT1NYVDQ1eXBIUkJlMnU1Q2lTdnVXdnpsT0hlN0tuQUVMU1Vwb21tZW1WbEV6clhTRWVoSDkiLCJtYWMiOiJiNGM2N2FiZTkwMzk1OThkMDc3MGEyOTc0NTFjNDk4Yjc0ZTFhNTU2NThmYjFiZjBlNzM4ZDExNTVmMGEwMzRlIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.7385</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721218416\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-308254118 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5YQflWm2dljwYG047t9akNiy9aKBn7vbP7mVMnSN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-308254118\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1359932050 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 17 Sep 2025 07:08:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImErRk9kdXZhSDlYSWNINk5SeGVGRHc9PSIsInZhbHVlIjoiMkluOXREVWlZU0dodFF2V1V0NlBhRzBSTDAvL0pDaHJ3Y0JoeGVaTnlrcU1ONzN5eDJOU09rUXAwSGJLYVNLaVRuMFNEZ1B5UUpSZVhiakg1WE1BY1l1UERyODhWN0o2THlXc08vNkR1cUcvTHFYWWVFV2QrdklNdCtMNzhRTGMiLCJtYWMiOiJmYTcwZDMxZmZlMjMzM2U0ZjAyZmFmMzkwN2UyYzkxZjczYTQ2MGMxNWE0ZDY5YjkxODg4MDA4ODliNzUwNjhhIiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 09:08:53 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6InFKTDZoV1d2Wnc2bVhEQ0NUU3J2dXc9PSIsInZhbHVlIjoibkNLcTRjM2pRYUNNcW1MbmtwMVVsTW5Rb2duZDFTcW0xaWpNaG5oK1IycDlxcjc4WjFhWVpBb21vSW1ETEtrcW1qM1JSTFcvbU12aythdFQreVE5dnV4ZW5vSUFwVTZod2hlMStwU0hCT2VxaGZrOVNnYmtGZG56TmFrZXc4NDkiLCJtYWMiOiJlZmFmM2I2MDMwOWMyNjhlMTlmYjU2MDM5OTExYmYzZTMxMDFlMTNlOTMyZmIwMWZhMjc5MTA4NzM1M2EwYmEwIiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 09:08:53 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImErRk9kdXZhSDlYSWNINk5SeGVGRHc9PSIsInZhbHVlIjoiMkluOXREVWlZU0dodFF2V1V0NlBhRzBSTDAvL0pDaHJ3Y0JoeGVaTnlrcU1ONzN5eDJOU09rUXAwSGJLYVNLaVRuMFNEZ1B5UUpSZVhiakg1WE1BY1l1UERyODhWN0o2THlXc08vNkR1cUcvTHFYWWVFV2QrdklNdCtMNzhRTGMiLCJtYWMiOiJmYTcwZDMxZmZlMjMzM2U0ZjAyZmFmMzkwN2UyYzkxZjczYTQ2MGMxNWE0ZDY5YjkxODg4MDA4ODliNzUwNjhhIiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 09:08:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6InFKTDZoV1d2Wnc2bVhEQ0NUU3J2dXc9PSIsInZhbHVlIjoibkNLcTRjM2pRYUNNcW1MbmtwMVVsTW5Rb2duZDFTcW0xaWpNaG5oK1IycDlxcjc4WjFhWVpBb21vSW1ETEtrcW1qM1JSTFcvbU12aythdFQreVE5dnV4ZW5vSUFwVTZod2hlMStwU0hCT2VxaGZrOVNnYmtGZG56TmFrZXc4NDkiLCJtYWMiOiJlZmFmM2I2MDMwOWMyNjhlMTlmYjU2MDM5OTExYmYzZTMxMDFlMTNlOTMyZmIwMWZhMjc5MTA4NzM1M2EwYmEwIiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 09:08:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1359932050\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-661920374 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1758092143</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-661920374\", {\"maxDepth\":0})</script>\n"}}